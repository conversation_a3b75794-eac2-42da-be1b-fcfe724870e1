import { NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-server'

export async function GET() {
  try {
    const { data: slots, error } = await supabaseAdmin
      .from('availability_slots')
      .select('*')
      .order('is_recurring', { ascending: false }) // Recurring slots first
      .order('day_of_week', { ascending: true })
      .order('date', { ascending: true })
      .order('start_time', { ascending: true })

    if (error) throw error

    return NextResponse.json(slots)
  } catch (error) {
    console.error('Error fetching availability slots:', error)
    return NextResponse.json(
      { error: 'Failed to fetch availability slots' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    
    const { data: slot, error } = await supabaseAdmin
      .from('availability_slots')
      .insert({
        date: body.date,
        start_time: body.start_time,
        end_time: body.end_time,
        is_available: body.is_available ?? true,
        max_appointments: body.max_appointments ?? 1,
        current_appointments: 0,
        notes: body.notes
      })
      .select()
      .single()

    if (error) throw error

    return NextResponse.json(slot)
  } catch (error) {
    console.error('Error creating availability slot:', error)
    return NextResponse.json(
      { error: 'Failed to create availability slot' },
      { status: 500 }
    )
  }
}
