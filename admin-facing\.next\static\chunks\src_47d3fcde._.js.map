{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,2KAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,2KAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,4HAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,2KAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,2KAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,4HAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,2KAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,IAAA,4HAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,2KAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,4HAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,6KAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,8KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,8KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,gLAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,IAAA,4HAAE,EACX,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6KAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,8OAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,+KAAsB;kBACrB,cAAA,6LAAC,gLAAuB;YACtB,aAAU;YACV,WAAW,IAAA,4HAAE,EACX,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,iLAAwB;oBACvB,WAAW,IAAA,4HAAE,EACX,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,8KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,6KAAoB;QACnB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,sLAA6B;8BAC5B,cAAA,6LAAC,wNAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,iLAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,kLAAyB;QACxB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,uLAA8B;QAC7B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,wOAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,yLAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,8OAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACb,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,IAAA,4HAAE,EAAC,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,KAGY;QAHZ,EACpB,SAAS,EACT,GAAG,OAC6B,GAHZ;IAIpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/lib/api-client.ts"], "sourcesContent": ["import { type Appointment, type Patient, type Service } from './supabase'\n\ninterface DashboardStats {\n  totalPatients: number\n  totalAppointments: number\n  totalServices: number\n  newMessages: number\n  pendingAppointments: number\n  confirmedAppointments: number\n  completedAppointments: number\n  inProgressAppointments: number\n}\n\nclass ApiClient {\n  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {\n    const response = await fetch(`/api${endpoint}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options?.headers,\n      },\n      ...options,\n    })\n\n    if (!response.ok) {\n      throw new Error(`API request failed: ${response.statusText}`)\n    }\n\n    return response.json()\n  }\n\n  // Dashboard\n  async getDashboardStats(): Promise<DashboardStats> {\n    return this.request<DashboardStats>('/dashboard/stats')\n  }\n\n  // Appointments\n  async getAppointments(): Promise<Appointment[]> {\n    return this.request<Appointment[]>('/appointments')\n  }\n\n  async getAppointment(id: string): Promise<Appointment> {\n    return this.request<Appointment>(`/appointments/${id}`)\n  }\n\n  async createAppointment(appointment: Partial<Appointment>): Promise<Appointment> {\n    return this.request<Appointment>('/appointments', {\n      method: 'POST',\n      body: JSON.stringify(appointment),\n    })\n  }\n\n  async updateAppointment(id: string, updates: Partial<Appointment>): Promise<Appointment> {\n    return this.request<Appointment>(`/appointments/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  async getPendingAppointments(): Promise<Appointment[]> {\n    return this.request<Appointment[]>('/appointments/pending')\n  }\n\n  async updatePendingAppointment(id: string, updates: Partial<Appointment>): Promise<Appointment> {\n    return this.request<Appointment>(`/appointments/pending?id=${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Patients\n  async getPatients(): Promise<Patient[]> {\n    return this.request<Patient[]>('/patients')\n  }\n\n  async getPatient(id: string): Promise<Patient> {\n    return this.request<Patient>(`/patients/${id}`)\n  }\n\n  async getPatientAppointments(patientId: string): Promise<any[]> {\n    return this.request<any[]>(`/patients/${patientId}/appointments`)\n  }\n\n  async createPatient(patient: Partial<Patient>): Promise<Patient> {\n    return this.request<Patient>('/patients', {\n      method: 'POST',\n      body: JSON.stringify(patient),\n    })\n  }\n\n  async updatePatient(id: string, updates: Partial<Patient>): Promise<Patient> {\n    return this.request<Patient>(`/patients/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Services\n  async getServices(): Promise<Service[]> {\n    return this.request<Service[]>('/services')\n  }\n\n  async getService(id: string): Promise<Service> {\n    return this.request<Service>(`/services/${id}`)\n  }\n\n  async createService(service: Partial<Service>): Promise<Service> {\n    return this.request<Service>('/services', {\n      method: 'POST',\n      body: JSON.stringify(service),\n    })\n  }\n\n  async updateService(id: string, updates: Partial<Service>): Promise<Service> {\n    return this.request<Service>(`/services/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Reports\n  async getReportsData(dateRange?: string): Promise<any> {\n    const params = dateRange ? `?dateRange=${dateRange}` : ''\n    return this.request<any>(`/reports${params}`)\n  }\n\n  // Medical Records\n  async getMedicalRecords(patientId?: string): Promise<any[]> {\n    const params = patientId ? `?patientId=${patientId}` : ''\n    return this.request<any[]>(`/medical-records${params}`)\n  }\n\n  async getMedicalRecord(id: string): Promise<any> {\n    return this.request<any>(`/medical-records/${id}`)\n  }\n\n  async createMedicalRecord(record: any): Promise<any> {\n    return this.request<any>('/medical-records', {\n      method: 'POST',\n      body: JSON.stringify(record),\n    })\n  }\n\n  async updateMedicalRecord(id: string, updates: any): Promise<any> {\n    return this.request<any>(`/medical-records/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Availability Slots\n  async getAvailabilitySlots(): Promise<any[]> {\n    return this.request<any[]>('/availability-slots')\n  }\n\n  async getAvailabilitySlot(id: string): Promise<any> {\n    return this.request<any>(`/availability-slots/${id}`)\n  }\n\n  async createAvailabilitySlot(slot: any): Promise<any> {\n    return this.request<any>('/availability-slots', {\n      method: 'POST',\n      body: JSON.stringify(slot),\n    })\n  }\n\n  async updateAvailabilitySlot(id: string, updates: any): Promise<any> {\n    return this.request<any>(`/availability-slots/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  async deleteAvailabilitySlot(id: string): Promise<void> {\n    return this.request<void>(`/availability-slots/${id}`, {\n      method: 'DELETE',\n    })\n  }\n\n  async checkSlotAvailability(date: string, time?: string): Promise<any> {\n    const params = time ? `?date=${date}&time=${time}` : `?date=${date}`\n    return this.request<any>(`/availability-slots/check${params}`)\n  }\n}\n\nexport const api = new ApiClient()\n"], "names": [], "mappings": ";;;;AAaA,MAAM;IACJ,MAAc,QAAW,QAAgB,EAAE,OAAqB,EAAc;QAC5E,MAAM,WAAW,MAAM,MAAM,AAAC,OAAe,OAAT,WAAY;YAC9C,SAAS;gBACP,gBAAgB;mBACb,oBAAA,8BAAA,QAAS,OAAO,AAAnB;YACF;YACA,GAAG,OAAO;QACZ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,AAAC,uBAA0C,OAApB,SAAS,UAAU;QAC5D;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,MAAM,oBAA6C;QACjD,OAAO,IAAI,CAAC,OAAO,CAAiB;IACtC;IAEA,eAAe;IACf,MAAM,kBAA0C;QAC9C,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,eAAe,EAAU,EAAwB;QACrD,OAAO,IAAI,CAAC,OAAO,CAAc,AAAC,iBAAmB,OAAH;IACpD;IAEA,MAAM,kBAAkB,WAAiC,EAAwB;QAC/E,OAAO,IAAI,CAAC,OAAO,CAAc,iBAAiB;YAChD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,kBAAkB,EAAU,EAAE,OAA6B,EAAwB;QACvF,OAAO,IAAI,CAAC,OAAO,CAAc,AAAC,iBAAmB,OAAH,KAAM;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,yBAAiD;QACrD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,yBAAyB,EAAU,EAAE,OAA6B,EAAwB;QAC9F,OAAO,IAAI,CAAC,OAAO,CAAc,AAAC,4BAA8B,OAAH,KAAM;YACjE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,WAAW;IACX,MAAM,cAAkC;QACtC,OAAO,IAAI,CAAC,OAAO,CAAY;IACjC;IAEA,MAAM,WAAW,EAAU,EAAoB;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAU,AAAC,aAAe,OAAH;IAC5C;IAEA,MAAM,uBAAuB,SAAiB,EAAkB;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAQ,AAAC,aAAsB,OAAV,WAAU;IACpD;IAEA,MAAM,cAAc,OAAyB,EAAoB;QAC/D,OAAO,IAAI,CAAC,OAAO,CAAU,aAAa;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,EAAU,EAAE,OAAyB,EAAoB;QAC3E,OAAO,IAAI,CAAC,OAAO,CAAU,AAAC,aAAe,OAAH,KAAM;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,WAAW;IACX,MAAM,cAAkC;QACtC,OAAO,IAAI,CAAC,OAAO,CAAY;IACjC;IAEA,MAAM,WAAW,EAAU,EAAoB;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAU,AAAC,aAAe,OAAH;IAC5C;IAEA,MAAM,cAAc,OAAyB,EAAoB;QAC/D,OAAO,IAAI,CAAC,OAAO,CAAU,aAAa;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,EAAU,EAAE,OAAyB,EAAoB;QAC3E,OAAO,IAAI,CAAC,OAAO,CAAU,AAAC,aAAe,OAAH,KAAM;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,UAAU;IACV,MAAM,eAAe,SAAkB,EAAgB;QACrD,MAAM,SAAS,YAAY,AAAC,cAAuB,OAAV,aAAc;QACvD,OAAO,IAAI,CAAC,OAAO,CAAM,AAAC,WAAiB,OAAP;IACtC;IAEA,kBAAkB;IAClB,MAAM,kBAAkB,SAAkB,EAAkB;QAC1D,MAAM,SAAS,YAAY,AAAC,cAAuB,OAAV,aAAc;QACvD,OAAO,IAAI,CAAC,OAAO,CAAQ,AAAC,mBAAyB,OAAP;IAChD;IAEA,MAAM,iBAAiB,EAAU,EAAgB;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAM,AAAC,oBAAsB,OAAH;IAC/C;IAEA,MAAM,oBAAoB,MAAW,EAAgB;QACnD,OAAO,IAAI,CAAC,OAAO,CAAM,oBAAoB;YAC3C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,oBAAoB,EAAU,EAAE,OAAY,EAAgB;QAChE,OAAO,IAAI,CAAC,OAAO,CAAM,AAAC,oBAAsB,OAAH,KAAM;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,qBAAqB;IACrB,MAAM,uBAAuC;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAQ;IAC7B;IAEA,MAAM,oBAAoB,EAAU,EAAgB;QAClD,OAAO,IAAI,CAAC,OAAO,CAAM,AAAC,uBAAyB,OAAH;IAClD;IAEA,MAAM,uBAAuB,IAAS,EAAgB;QACpD,OAAO,IAAI,CAAC,OAAO,CAAM,uBAAuB;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,uBAAuB,EAAU,EAAE,OAAY,EAAgB;QACnE,OAAO,IAAI,CAAC,OAAO,CAAM,AAAC,uBAAyB,OAAH,KAAM;YACpD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,uBAAuB,EAAU,EAAiB;QACtD,OAAO,IAAI,CAAC,OAAO,CAAO,AAAC,uBAAyB,OAAH,KAAM;YACrD,QAAQ;QACV;IACF;IAEA,MAAM,sBAAsB,IAAY,EAAE,IAAa,EAAgB;QACrE,MAAM,SAAS,OAAO,AAAC,SAAqB,OAAb,MAAK,UAAa,OAAL,QAAS,AAAC,SAAa,OAAL;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAM,AAAC,4BAAkC,OAAP;IACvD;AACF;AAEO,MAAM,MAAM,IAAI", "debugId": null}}, {"offset": {"line": 701, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,IAAA,0KAAG,EACvB;AAGF,MAAM,sBAAQ,2KAAgB,MAI5B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,4KAAmB;QAClB,KAAK;QACL,WAAW,IAAA,4HAAE,EAAC,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;;AAGb,MAAM,WAAW,GAAG,4KAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,2KAAgB,MAG7B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,6KAAqB;QACpB,WAAW,IAAA,4HAAE,EACX,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,6LAAC,8KAAsB;YACrB,WAAW,IAAA,4HAAE,EACX;;;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,6KAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 786, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {\n  // Additional props can be added here if needed\n}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;AAOA,MAAM,yBAAW,2KAAgB,MAC/B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;IACtB,qBACE,6LAAC;QACC,WAAW,IAAA,4HAAE,EACX,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 821, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,6KAAoB;AAEnC,MAAM,gBAAgB,gLAAuB;AAE7C,MAAM,eAAe,+KAAsB;AAE3C,MAAM,cAAc,8KAAqB;AAEzC,MAAM,8BAAgB,2KAAgB,CAGpC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,gLAAuB;QACtB,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,2JACA;QAED,GAAG,KAAK;;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,gLAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,2KAAgB,OAGpC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,gLAAuB;gBACtB,KAAK;gBACL,WAAW,IAAA,4HAAE,EACX,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,8KAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,oMAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,gLAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe;QAAC,EACpB,SAAS,EACT,GAAG,OACkC;yBACrC,6LAAC;QACC,WAAW,IAAA,4HAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe;QAAC,EACpB,SAAS,EACT,GAAG,OACkC;yBACrC,6LAAC;QACC,WAAW,IAAA,4HAAE,EACX,iEACA;QAED,GAAG,KAAK;;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,2KAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,8KAAqB;QACpB,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,qDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,8KAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,2KAAgB,OAGxC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,oLAA2B;QAC1B,KAAK;QACL,WAAW,IAAA,4HAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,oLAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 998, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/settings/slot-management-modal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Switch } from '@/components/ui/switch'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Clock, Calendar, Users, Save, X } from 'lucide-react'\n\ninterface SlotData {\n  id?: string\n  dayOfWeek: number // 0=Sunday, 1=Monday, ..., 6=Saturday\n  startTime: string\n  endTime: string\n  maxAppointments: number\n  isActive: boolean\n  notes?: string\n}\n\ninterface SlotManagementModalProps {\n  isOpen: boolean\n  onClose: () => void\n  onSave: (slotData: SlotData) => Promise<void>\n  editingSlot?: SlotData | null\n  mode: 'create' | 'edit'\n}\n\nconst DAYS_OF_WEEK = [\n  { value: 0, label: 'Sunday' },\n  { value: 1, label: 'Monday' },\n  { value: 2, label: 'Tuesday' },\n  { value: 3, label: 'Wednesday' },\n  { value: 4, label: 'Thursday' },\n  { value: 5, label: 'Friday' },\n  { value: 6, label: 'Saturday' },\n]\n\nexport function SlotManagementModal({\n  isOpen,\n  onClose,\n  onSave,\n  editingSlot,\n  mode\n}: SlotManagementModalProps) {\n  const [formData, setFormData] = useState<SlotData>({\n    dayOfWeek: 1, // Default to Monday\n    startTime: '09:00',\n    endTime: '10:00',\n    maxAppointments: 2,\n    isActive: true,\n    notes: ''\n  })\n  const [loading, setLoading] = useState(false)\n  const [errors, setErrors] = useState<Record<string, string>>({})\n\n  useEffect(() => {\n    if (editingSlot && mode === 'edit') {\n      setFormData({\n        id: editingSlot.id,\n        dayOfWeek: editingSlot.dayOfWeek,\n        startTime: editingSlot.startTime,\n        endTime: editingSlot.endTime,\n        maxAppointments: editingSlot.maxAppointments,\n        isActive: editingSlot.isActive,\n        notes: editingSlot.notes || ''\n      })\n    } else {\n      // Reset form for create mode\n      setFormData({\n        dayOfWeek: 1,\n        startTime: '09:00',\n        endTime: '10:00',\n        maxAppointments: 2,\n        isActive: true,\n        notes: ''\n      })\n    }\n    setErrors({})\n  }, [editingSlot, mode, isOpen])\n\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {}\n\n    if (!formData.startTime) {\n      newErrors.startTime = 'Start time is required'\n    }\n    if (!formData.endTime) {\n      newErrors.endTime = 'End time is required'\n    }\n    if (formData.startTime && formData.endTime && formData.startTime >= formData.endTime) {\n      newErrors.endTime = 'End time must be after start time'\n    }\n    if (formData.maxAppointments < 1) {\n      newErrors.maxAppointments = 'Must allow at least 1 appointment'\n    }\n    if (formData.maxAppointments > 20) {\n      newErrors.maxAppointments = 'Maximum 20 appointments per slot'\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) {\n      return\n    }\n\n    setLoading(true)\n    try {\n      await onSave(formData)\n      onClose()\n    } catch (error) {\n      console.error('Error saving slot:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleInputChange = (field: keyof SlotData, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }))\n    }\n  }\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"sm:max-w-[500px]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Calendar className=\"h-5 w-5\" />\n            {mode === 'create' ? 'Add New Time Slot' : 'Edit Time Slot'}\n          </DialogTitle>\n          <DialogDescription>\n            Configure availability slot settings for appointment booking.\n          </DialogDescription>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* Day of Week */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"dayOfWeek\">Day of Week</Label>\n            <Select\n              value={formData.dayOfWeek.toString()}\n              onValueChange={(value) => handleInputChange('dayOfWeek', parseInt(value))}\n            >\n              <SelectTrigger>\n                <SelectValue placeholder=\"Select day\" />\n              </SelectTrigger>\n              <SelectContent>\n                {DAYS_OF_WEEK.map((day) => (\n                  <SelectItem key={day.value} value={day.value.toString()}>\n                    {day.label}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Time Range */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"startTime\">Start Time</Label>\n              <div className=\"relative\">\n                <Clock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  id=\"startTime\"\n                  type=\"time\"\n                  value={formData.startTime}\n                  onChange={(e) => handleInputChange('startTime', e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n              {errors.startTime && (\n                <p className=\"text-sm text-red-600\">{errors.startTime}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"endTime\">End Time</Label>\n              <div className=\"relative\">\n                <Clock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  id=\"endTime\"\n                  type=\"time\"\n                  value={formData.endTime}\n                  onChange={(e) => handleInputChange('endTime', e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n              {errors.endTime && (\n                <p className=\"text-sm text-red-600\">{errors.endTime}</p>\n              )}\n            </div>\n          </div>\n\n          {/* Max Appointments */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"maxAppointments\">Maximum Appointments</Label>\n            <div className=\"relative\">\n              <Users className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n              <Input\n                id=\"maxAppointments\"\n                type=\"number\"\n                min=\"1\"\n                max=\"20\"\n                value={formData.maxAppointments}\n                onChange={(e) => handleInputChange('maxAppointments', parseInt(e.target.value) || 1)}\n                className=\"pl-10\"\n              />\n            </div>\n            {errors.maxAppointments && (\n              <p className=\"text-sm text-red-600\">{errors.maxAppointments}</p>\n            )}\n            <p className=\"text-sm text-muted-foreground\">\n              Number of appointments that can be booked in this time slot\n            </p>\n          </div>\n\n          {/* Active Status */}\n          <div className=\"flex items-center justify-between\">\n            <div className=\"space-y-0.5\">\n              <Label htmlFor=\"isActive\">Active Status</Label>\n              <p className=\"text-sm text-muted-foreground\">\n                Enable this slot for patient booking\n              </p>\n            </div>\n            <Switch\n              id=\"isActive\"\n              checked={formData.isActive}\n              onCheckedChange={(checked) => handleInputChange('isActive', checked)}\n            />\n          </div>\n\n          {/* Notes */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"notes\">Notes (Optional)</Label>\n            <Textarea\n              id=\"notes\"\n              placeholder=\"Add any special notes for this time slot...\"\n              value={formData.notes}\n              onChange={(e) => handleInputChange('notes', e.target.value)}\n              rows={3}\n            />\n          </div>\n\n          <DialogFooter>\n            <Button type=\"button\" variant=\"outline\" onClick={onClose}>\n              <X className=\"h-4 w-4 mr-2\" />\n              Cancel\n            </Button>\n            <Button type=\"submit\" disabled={loading}>\n              <Save className=\"h-4 w-4 mr-2\" />\n              {loading ? 'Saving...' : mode === 'create' ? 'Create Slot' : 'Update Slot'}\n            </Button>\n          </DialogFooter>\n        </form>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AAOA;AAAA;AAAA;AAAA;AAAA;;;AAvBA;;;;;;;;;;AA2CA,MAAM,eAAe;IACnB;QAAE,OAAO;QAAG,OAAO;IAAS;IAC5B;QAAE,OAAO;QAAG,OAAO;IAAS;IAC5B;QAAE,OAAO;QAAG,OAAO;IAAU;IAC7B;QAAE,OAAO;QAAG,OAAO;IAAY;IAC/B;QAAE,OAAO;QAAG,OAAO;IAAW;IAC9B;QAAE,OAAO;QAAG,OAAO;IAAS;IAC5B;QAAE,OAAO;QAAG,OAAO;IAAW;CAC/B;AAEM,SAAS,oBAAoB,KAMT;QANS,EAClC,MAAM,EACN,OAAO,EACP,MAAM,EACN,WAAW,EACX,IAAI,EACqB,GANS;;IAOlC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAW;QACjD,WAAW;QACX,WAAW;QACX,SAAS;QACT,iBAAiB;QACjB,UAAU;QACV,OAAO;IACT;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAyB,CAAC;IAE9D,IAAA,0KAAS;yCAAC;YACR,IAAI,eAAe,SAAS,QAAQ;gBAClC,YAAY;oBACV,IAAI,YAAY,EAAE;oBAClB,WAAW,YAAY,SAAS;oBAChC,WAAW,YAAY,SAAS;oBAChC,SAAS,YAAY,OAAO;oBAC5B,iBAAiB,YAAY,eAAe;oBAC5C,UAAU,YAAY,QAAQ;oBAC9B,OAAO,YAAY,KAAK,IAAI;gBAC9B;YACF,OAAO;gBACL,6BAA6B;gBAC7B,YAAY;oBACV,WAAW;oBACX,WAAW;oBACX,SAAS;oBACT,iBAAiB;oBACjB,UAAU;oBACV,OAAO;gBACT;YACF;YACA,UAAU,CAAC;QACb;wCAAG;QAAC;QAAa;QAAM;KAAO;IAE9B,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,SAAS,EAAE;YACvB,UAAU,SAAS,GAAG;QACxB;QACA,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,UAAU,OAAO,GAAG;QACtB;QACA,IAAI,SAAS,SAAS,IAAI,SAAS,OAAO,IAAI,SAAS,SAAS,IAAI,SAAS,OAAO,EAAE;YACpF,UAAU,OAAO,GAAG;QACtB;QACA,IAAI,SAAS,eAAe,GAAG,GAAG;YAChC,UAAU,eAAe,GAAG;QAC9B;QACA,IAAI,SAAS,eAAe,GAAG,IAAI;YACjC,UAAU,eAAe,GAAG;QAC9B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,WAAW;QACX,IAAI;YACF,MAAM,OAAO;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAuB;QAChD,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,sCAAsC;QACtC,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QAC7C;IACF;IAEA,qBACE,6LAAC,+IAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,sJAAa;YAAC,WAAU;;8BACvB,6LAAC,qJAAY;;sCACX,6LAAC,oJAAW;4BAAC,WAAU;;8CACrB,6LAAC,yNAAQ;oCAAC,WAAU;;;;;;gCACnB,SAAS,WAAW,sBAAsB;;;;;;;sCAE7C,6LAAC,0JAAiB;sCAAC;;;;;;;;;;;;8BAKrB,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6IAAK;oCAAC,SAAQ;8CAAY;;;;;;8CAC3B,6LAAC,+IAAM;oCACL,OAAO,SAAS,SAAS,CAAC,QAAQ;oCAClC,eAAe,CAAC,QAAU,kBAAkB,aAAa,SAAS;;sDAElE,6LAAC,sJAAa;sDACZ,cAAA,6LAAC,oJAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,6LAAC,sJAAa;sDACX,aAAa,GAAG,CAAC,CAAC,oBACjB,6LAAC,mJAAU;oDAAiB,OAAO,IAAI,KAAK,CAAC,QAAQ;8DAClD,IAAI,KAAK;mDADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sCASlC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6IAAK;4CAAC,SAAQ;sDAAY;;;;;;sDAC3B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gNAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC,6IAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,SAAS;oDACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC9D,WAAU;;;;;;;;;;;;wCAGb,OAAO,SAAS,kBACf,6LAAC;4CAAE,WAAU;sDAAwB,OAAO,SAAS;;;;;;;;;;;;8CAIzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6IAAK;4CAAC,SAAQ;sDAAU;;;;;;sDACzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gNAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC,6IAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC5D,WAAU;;;;;;;;;;;;wCAGb,OAAO,OAAO,kBACb,6LAAC;4CAAE,WAAU;sDAAwB,OAAO,OAAO;;;;;;;;;;;;;;;;;;sCAMzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6IAAK;oCAAC,SAAQ;8CAAkB;;;;;;8CACjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gNAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC,6IAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,OAAO,SAAS,eAAe;4CAC/B,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4CAClF,WAAU;;;;;;;;;;;;gCAGb,OAAO,eAAe,kBACrB,6LAAC;oCAAE,WAAU;8CAAwB,OAAO,eAAe;;;;;;8CAE7D,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAM/C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6IAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAI/C,6LAAC,+IAAM;oCACL,IAAG;oCACH,SAAS,SAAS,QAAQ;oCAC1B,iBAAiB,CAAC,UAAY,kBAAkB,YAAY;;;;;;;;;;;;sCAKhE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6IAAK;oCAAC,SAAQ;8CAAQ;;;;;;8CACvB,6LAAC,mJAAQ;oCACP,IAAG;oCACH,aAAY;oCACZ,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oCAC1D,MAAM;;;;;;;;;;;;sCAIV,6LAAC,qJAAY;;8CACX,6LAAC,+IAAM;oCAAC,MAAK;oCAAS,SAAQ;oCAAU,SAAS;;sDAC/C,6LAAC,oMAAC;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGhC,6LAAC,+IAAM;oCAAC,MAAK;oCAAS,UAAU;;sDAC9B,6LAAC,6MAAI;4CAAC,WAAU;;;;;;wCACf,UAAU,cAAc,SAAS,WAAW,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3E;GAnOgB;KAAA", "debugId": null}}, {"offset": {"line": 1565, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,2KAAgB,MAG/B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,+KAAsB;QACrB,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,oLAA2B;YAC1B,WAAW,IAAA,4HAAE,EAAC;sBAEd,cAAA,6LAAC,gNAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,+KAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1619, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/settings/bulk-slot-modal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Clock, Calendar, Users, Save, X, Copy } from 'lucide-react'\n\ninterface BulkSlotData {\n  startTime: string\n  endTime: string\n  maxAppointments: number\n  selectedDays: number[]\n  notes?: string\n}\n\ninterface BulkSlotModalProps {\n  isOpen: boolean\n  onClose: () => void\n  onSave: (bulkData: BulkSlotData) => Promise<void>\n}\n\nconst DAYS_OF_WEEK = [\n  { value: 0, label: 'Sunday', short: 'Sun' },\n  { value: 1, label: 'Monday', short: 'Mon' },\n  { value: 2, label: 'Tuesday', short: 'Tue' },\n  { value: 3, label: 'Wednesday', short: 'Wed' },\n  { value: 4, label: 'Thursday', short: 'Thu' },\n  { value: 5, label: 'Friday', short: 'Fri' },\n  { value: 6, label: 'Saturday', short: 'Sat' },\n]\n\nexport function BulkSlotModal({ isOpen, onClose, onSave }: BulkSlotModalProps) {\n  const [formData, setFormData] = useState<BulkSlotData>({\n    startTime: '09:00',\n    endTime: '10:00',\n    maxAppointments: 2,\n    selectedDays: [1, 2, 3, 4, 5], // Default to weekdays\n    notes: ''\n  })\n  const [loading, setLoading] = useState(false)\n  const [errors, setErrors] = useState<Record<string, string>>({})\n\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {}\n\n    if (!formData.startTime) {\n      newErrors.startTime = 'Start time is required'\n    }\n    if (!formData.endTime) {\n      newErrors.endTime = 'End time is required'\n    }\n    if (formData.startTime && formData.endTime && formData.startTime >= formData.endTime) {\n      newErrors.endTime = 'End time must be after start time'\n    }\n    if (formData.maxAppointments < 1) {\n      newErrors.maxAppointments = 'Must allow at least 1 appointment'\n    }\n    if (formData.selectedDays.length === 0) {\n      newErrors.selectedDays = 'Please select at least one day'\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) {\n      return\n    }\n\n    setLoading(true)\n    try {\n      await onSave(formData)\n      onClose()\n      // Reset form\n      setFormData({\n        startTime: '09:00',\n        endTime: '10:00',\n        maxAppointments: 2,\n        selectedDays: [1, 2, 3, 4, 5],\n        notes: ''\n      })\n    } catch (error) {\n      console.error('Error saving bulk slots:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleInputChange = (field: keyof BulkSlotData, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }))\n    }\n  }\n\n  const handleDayToggle = (dayValue: number, checked: boolean) => {\n    const newSelectedDays = checked\n      ? [...formData.selectedDays, dayValue]\n      : formData.selectedDays.filter(day => day !== dayValue)\n    \n    handleInputChange('selectedDays', newSelectedDays)\n  }\n\n  const selectWeekdays = () => {\n    handleInputChange('selectedDays', [1, 2, 3, 4, 5])\n  }\n\n  const selectWeekend = () => {\n    handleInputChange('selectedDays', [0, 6])\n  }\n\n  const selectAllDays = () => {\n    handleInputChange('selectedDays', [0, 1, 2, 3, 4, 5, 6])\n  }\n\n  const clearAllDays = () => {\n    handleInputChange('selectedDays', [])\n  }\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Copy className=\"h-5 w-5\" />\n            Bulk Create Time Slots\n          </DialogTitle>\n          <DialogDescription>\n            Create the same time slot for multiple days at once.\n          </DialogDescription>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* Time Range */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"startTime\">Start Time</Label>\n              <div className=\"relative\">\n                <Clock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  id=\"startTime\"\n                  type=\"time\"\n                  value={formData.startTime}\n                  onChange={(e) => handleInputChange('startTime', e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n              {errors.startTime && (\n                <p className=\"text-sm text-red-600\">{errors.startTime}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"endTime\">End Time</Label>\n              <div className=\"relative\">\n                <Clock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  id=\"endTime\"\n                  type=\"time\"\n                  value={formData.endTime}\n                  onChange={(e) => handleInputChange('endTime', e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n              {errors.endTime && (\n                <p className=\"text-sm text-red-600\">{errors.endTime}</p>\n              )}\n            </div>\n          </div>\n\n          {/* Max Appointments */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"maxAppointments\">Maximum Appointments</Label>\n            <div className=\"relative\">\n              <Users className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n              <Input\n                id=\"maxAppointments\"\n                type=\"number\"\n                min=\"1\"\n                max=\"20\"\n                value={formData.maxAppointments}\n                onChange={(e) => handleInputChange('maxAppointments', parseInt(e.target.value) || 1)}\n                className=\"pl-10\"\n              />\n            </div>\n            {errors.maxAppointments && (\n              <p className=\"text-sm text-red-600\">{errors.maxAppointments}</p>\n            )}\n          </div>\n\n          {/* Day Selection */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <Label>Select Days</Label>\n              <div className=\"flex gap-2\">\n                <Button type=\"button\" variant=\"outline\" size=\"sm\" onClick={selectWeekdays}>\n                  Weekdays\n                </Button>\n                <Button type=\"button\" variant=\"outline\" size=\"sm\" onClick={selectWeekend}>\n                  Weekend\n                </Button>\n                <Button type=\"button\" variant=\"outline\" size=\"sm\" onClick={selectAllDays}>\n                  All\n                </Button>\n                <Button type=\"button\" variant=\"outline\" size=\"sm\" onClick={clearAllDays}>\n                  Clear\n                </Button>\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-7 gap-2\">\n              {DAYS_OF_WEEK.map((day) => (\n                <div key={day.value} className=\"flex flex-col items-center space-y-2\">\n                  <Label htmlFor={`day-${day.value}`} className=\"text-sm font-medium\">\n                    {day.short}\n                  </Label>\n                  <Checkbox\n                    id={`day-${day.value}`}\n                    checked={formData.selectedDays.includes(day.value)}\n                    onCheckedChange={(checked) => handleDayToggle(day.value, checked as boolean)}\n                  />\n                </div>\n              ))}\n            </div>\n            {errors.selectedDays && (\n              <p className=\"text-sm text-red-600\">{errors.selectedDays}</p>\n            )}\n          </div>\n\n          {/* Selected Days Preview */}\n          {formData.selectedDays.length > 0 && (\n            <div className=\"p-4 bg-muted/50 rounded-lg\">\n              <p className=\"text-sm font-medium mb-2\">Creating slots for:</p>\n              <div className=\"flex flex-wrap gap-2\">\n                {formData.selectedDays\n                  .sort((a, b) => a - b)\n                  .map(dayValue => {\n                    const day = DAYS_OF_WEEK.find(d => d.value === dayValue)\n                    return (\n                      <span key={dayValue} className=\"px-2 py-1 bg-primary/10 text-primary rounded text-sm\">\n                        {day?.label}\n                      </span>\n                    )\n                  })}\n              </div>\n              <p className=\"text-sm text-muted-foreground mt-2\">\n                Time: {formData.startTime} - {formData.endTime} | \n                Capacity: {formData.maxAppointments} appointments each\n              </p>\n            </div>\n          )}\n\n          <DialogFooter>\n            <Button type=\"button\" variant=\"outline\" onClick={onClose}>\n              <X className=\"h-4 w-4 mr-2\" />\n              Cancel\n            </Button>\n            <Button type=\"submit\" disabled={loading || formData.selectedDays.length === 0}>\n              <Save className=\"h-4 w-4 mr-2\" />\n              {loading ? 'Creating...' : `Create ${formData.selectedDays.length} Slots`}\n            </Button>\n          </DialogFooter>\n        </form>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAAA;AAAA;;;AAfA;;;;;;;;AA+BA,MAAM,eAAe;IACnB;QAAE,OAAO;QAAG,OAAO;QAAU,OAAO;IAAM;IAC1C;QAAE,OAAO;QAAG,OAAO;QAAU,OAAO;IAAM;IAC1C;QAAE,OAAO;QAAG,OAAO;QAAW,OAAO;IAAM;IAC3C;QAAE,OAAO;QAAG,OAAO;QAAa,OAAO;IAAM;IAC7C;QAAE,OAAO;QAAG,OAAO;QAAY,OAAO;IAAM;IAC5C;QAAE,OAAO;QAAG,OAAO;QAAU,OAAO;IAAM;IAC1C;QAAE,OAAO;QAAG,OAAO;QAAY,OAAO;IAAM;CAC7C;AAEM,SAAS,cAAc,KAA+C;QAA/C,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAsB,GAA/C;;IAC5B,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAe;QACrD,WAAW;QACX,SAAS;QACT,iBAAiB;QACjB,cAAc;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAC7B,OAAO;IACT;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAyB,CAAC;IAE9D,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,SAAS,EAAE;YACvB,UAAU,SAAS,GAAG;QACxB;QACA,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,UAAU,OAAO,GAAG;QACtB;QACA,IAAI,SAAS,SAAS,IAAI,SAAS,OAAO,IAAI,SAAS,SAAS,IAAI,SAAS,OAAO,EAAE;YACpF,UAAU,OAAO,GAAG;QACtB;QACA,IAAI,SAAS,eAAe,GAAG,GAAG;YAChC,UAAU,eAAe,GAAG;QAC9B;QACA,IAAI,SAAS,YAAY,CAAC,MAAM,KAAK,GAAG;YACtC,UAAU,YAAY,GAAG;QAC3B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,WAAW;QACX,IAAI;YACF,MAAM,OAAO;YACb;YACA,aAAa;YACb,YAAY;gBACV,WAAW;gBACX,SAAS;gBACT,iBAAiB;gBACjB,cAAc;oBAAC;oBAAG;oBAAG;oBAAG;oBAAG;iBAAE;gBAC7B,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC,OAA2B;QACpD,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QAC7C;IACF;IAEA,MAAM,kBAAkB,CAAC,UAAkB;QACzC,MAAM,kBAAkB,UACpB;eAAI,SAAS,YAAY;YAAE;SAAS,GACpC,SAAS,YAAY,CAAC,MAAM,CAAC,CAAA,MAAO,QAAQ;QAEhD,kBAAkB,gBAAgB;IACpC;IAEA,MAAM,iBAAiB;QACrB,kBAAkB,gBAAgB;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;IACnD;IAEA,MAAM,gBAAgB;QACpB,kBAAkB,gBAAgB;YAAC;YAAG;SAAE;IAC1C;IAEA,MAAM,gBAAgB;QACpB,kBAAkB,gBAAgB;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;IACzD;IAEA,MAAM,eAAe;QACnB,kBAAkB,gBAAgB,EAAE;IACtC;IAEA,qBACE,6LAAC,+IAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,sJAAa;YAAC,WAAU;;8BACvB,6LAAC,qJAAY;;sCACX,6LAAC,oJAAW;4BAAC,WAAU;;8CACrB,6LAAC,6MAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG9B,6LAAC,0JAAiB;sCAAC;;;;;;;;;;;;8BAKrB,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6IAAK;4CAAC,SAAQ;sDAAY;;;;;;sDAC3B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gNAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC,6IAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,SAAS;oDACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC9D,WAAU;;;;;;;;;;;;wCAGb,OAAO,SAAS,kBACf,6LAAC;4CAAE,WAAU;sDAAwB,OAAO,SAAS;;;;;;;;;;;;8CAIzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6IAAK;4CAAC,SAAQ;sDAAU;;;;;;sDACzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gNAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC,6IAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC5D,WAAU;;;;;;;;;;;;wCAGb,OAAO,OAAO,kBACb,6LAAC;4CAAE,WAAU;sDAAwB,OAAO,OAAO;;;;;;;;;;;;;;;;;;sCAMzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6IAAK;oCAAC,SAAQ;8CAAkB;;;;;;8CACjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gNAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC,6IAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,OAAO,SAAS,eAAe;4CAC/B,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4CAClF,WAAU;;;;;;;;;;;;gCAGb,OAAO,eAAe,kBACrB,6LAAC;oCAAE,WAAU;8CAAwB,OAAO,eAAe;;;;;;;;;;;;sCAK/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6IAAK;sDAAC;;;;;;sDACP,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+IAAM;oDAAC,MAAK;oDAAS,SAAQ;oDAAU,MAAK;oDAAK,SAAS;8DAAgB;;;;;;8DAG3E,6LAAC,+IAAM;oDAAC,MAAK;oDAAS,SAAQ;oDAAU,MAAK;oDAAK,SAAS;8DAAe;;;;;;8DAG1E,6LAAC,+IAAM;oDAAC,MAAK;oDAAS,SAAQ;oDAAU,MAAK;oDAAK,SAAS;8DAAe;;;;;;8DAG1E,6LAAC,+IAAM;oDAAC,MAAK;oDAAS,SAAQ;oDAAU,MAAK;oDAAK,SAAS;8DAAc;;;;;;;;;;;;;;;;;;8CAM7E,6LAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,6LAAC;4CAAoB,WAAU;;8DAC7B,6LAAC,6IAAK;oDAAC,SAAS,AAAC,OAAgB,OAAV,IAAI,KAAK;oDAAI,WAAU;8DAC3C,IAAI,KAAK;;;;;;8DAEZ,6LAAC,mJAAQ;oDACP,IAAI,AAAC,OAAgB,OAAV,IAAI,KAAK;oDACpB,SAAS,SAAS,YAAY,CAAC,QAAQ,CAAC,IAAI,KAAK;oDACjD,iBAAiB,CAAC,UAAY,gBAAgB,IAAI,KAAK,EAAE;;;;;;;2CAPnD,IAAI,KAAK;;;;;;;;;;gCAYtB,OAAO,YAAY,kBAClB,6LAAC;oCAAE,WAAU;8CAAwB,OAAO,YAAY;;;;;;;;;;;;wBAK3D,SAAS,YAAY,CAAC,MAAM,GAAG,mBAC9B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAA2B;;;;;;8CACxC,6LAAC;oCAAI,WAAU;8CACZ,SAAS,YAAY,CACnB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,GACnB,GAAG,CAAC,CAAA;wCACH,MAAM,MAAM,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;wCAC/C,qBACE,6LAAC;4CAAoB,WAAU;sDAC5B,gBAAA,0BAAA,IAAK,KAAK;2CADF;;;;;oCAIf;;;;;;8CAEJ,6LAAC;oCAAE,WAAU;;wCAAqC;wCACzC,SAAS,SAAS;wCAAC;wCAAI,SAAS,OAAO;wCAAC;wCACpC,SAAS,eAAe;wCAAC;;;;;;;;;;;;;sCAK1C,6LAAC,qJAAY;;8CACX,6LAAC,+IAAM;oCAAC,MAAK;oCAAS,SAAQ;oCAAU,SAAS;;sDAC/C,6LAAC,oMAAC;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGhC,6LAAC,+IAAM;oCAAC,MAAK;oCAAS,UAAU,WAAW,SAAS,YAAY,CAAC,MAAM,KAAK;;sDAC1E,6LAAC,6MAAI;4CAAC,WAAU;;;;;;wCACf,UAAU,gBAAgB,AAAC,UAAsC,OAA7B,SAAS,YAAY,CAAC,MAAM,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhF;GA9OgB;KAAA", "debugId": null}}, {"offset": {"line": 2251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/settings/settings-page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from '@/components/ui/table'\nimport {\n  Settings,\n  Shield,\n  Database,\n  Clock,\n  Save,\n  RefreshCw,\n  Plus,\n  Edit,\n  Trash2,\n  User,\n  Mail,\n  Phone,\n  MapPin,\n  Globe,\n  Calendar\n} from 'lucide-react'\nimport { api } from '@/lib/api-client'\nimport { SlotManagementModal } from './slot-management-modal'\nimport { BulkSlotModal } from './bulk-slot-modal'\n\ninterface ClinicSettings {\n  name: string\n  address: string\n  phone: string\n  email: string\n  website: string\n  description: string\n  workingHours: {\n    [key: string]: { open: string; close: string; closed: boolean }\n  }\n}\n\ninterface AdminUser {\n  id: string\n  name: string\n  email: string\n  role: 'admin' | 'staff' | 'doctor'\n  status: 'active' | 'inactive'\n  lastLogin: string\n}\n\ninterface AvailabilitySlot {\n  id: string\n  day: string\n  dayOfWeek?: number\n  startTime: string\n  endTime: string\n  maxAppointments: number\n  isActive: boolean\n  isRecurring?: boolean\n  date?: string\n  notes?: string\n  currentAppointments?: number\n}\n\nexport function SettingsPage() {\n  const [clinicSettings, setClinicSettings] = useState<ClinicSettings>({\n    name: \"Dr. Priya Sharma's Dental Wellness Studio\",\n    address: \"123 Connaught Place, New Delhi, 110001\",\n    phone: \"+91 11 2334 5678\",\n    email: \"<EMAIL>\",\n    website: \"www.dentalwellness.com\",\n    description: \"Premier dental care with state-of-the-art technology and personalized treatment plans.\",\n    workingHours: {\n      monday: { open: '09:00', close: '18:00', closed: false },\n      tuesday: { open: '09:00', close: '18:00', closed: false },\n      wednesday: { open: '09:00', close: '18:00', closed: false },\n      thursday: { open: '09:00', close: '18:00', closed: false },\n      friday: { open: '09:00', close: '18:00', closed: false },\n      saturday: { open: '10:00', close: '16:00', closed: false },\n      sunday: { open: '10:00', close: '14:00', closed: true }\n    }\n  })\n\n  const [adminUsers, setAdminUsers] = useState<AdminUser[]>([\n    {\n      id: '1',\n      name: 'Dr. Priya Sharma',\n      email: '<EMAIL>',\n      role: 'admin',\n      status: 'active',\n      lastLogin: new Date().toISOString()\n    },\n    {\n      id: '2',\n      name: 'Nurse Anita',\n      email: '<EMAIL>',\n      role: 'staff',\n      status: 'active',\n      lastLogin: new Date(Date.now() - 3600000).toISOString()\n    }\n  ])\n\n  const [availabilitySlots, setAvailabilitySlots] = useState<AvailabilitySlot[]>([])\n  const [loading, setLoading] = useState(false)\n  const [activeTab, setActiveTab] = useState('general')\n\n  // Modal states\n  const [showSlotModal, setShowSlotModal] = useState(false)\n  const [showBulkModal, setShowBulkModal] = useState(false)\n  const [editingSlot, setEditingSlot] = useState<AvailabilitySlot | null>(null)\n  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create')\n\n  // Load availability slots on component mount\n  useEffect(() => {\n    if (activeTab === 'availability') {\n      loadAvailabilitySlots()\n    }\n  }, [activeTab])\n\n  const loadAvailabilitySlots = async () => {\n    try {\n      setLoading(true)\n      const slots = await api.getAvailabilitySlots()\n\n      // Transform database slots to component format\n      const transformedSlots = slots.map(slot => {\n        const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']\n\n        return {\n          id: slot.id,\n          day: slot.is_recurring\n            ? dayNames[slot.day_of_week] || 'Unknown'\n            : slot.date ? new Date(slot.date).toLocaleDateString('en-US', { weekday: 'long' }) : 'Unknown',\n          dayOfWeek: slot.day_of_week,\n          startTime: slot.start_time,\n          endTime: slot.end_time,\n          maxAppointments: slot.max_appointments,\n          isActive: slot.is_available,\n          isRecurring: slot.is_recurring,\n          date: slot.date,\n          notes: slot.notes,\n          currentAppointments: slot.current_appointments || 0\n        }\n      })\n\n      // Sort by day of week for recurring slots, then by date for specific slots\n      transformedSlots.sort((a, b) => {\n        if (a.isRecurring && b.isRecurring) {\n          return (a.dayOfWeek || 0) - (b.dayOfWeek || 0)\n        }\n        if (a.isRecurring && !b.isRecurring) return -1\n        if (!a.isRecurring && b.isRecurring) return 1\n        return (a.date || '').localeCompare(b.date || '')\n      })\n\n      setAvailabilitySlots(transformedSlots)\n    } catch (error) {\n      console.error('Error loading availability slots:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSaveSettings = async () => {\n    setLoading(true)\n    try {\n      // Mock save operation - replace with actual API call\n      await new Promise(resolve => setTimeout(resolve, 1000))\n      console.log('Settings saved:', clinicSettings)\n    } catch (error) {\n      console.error('Error saving settings:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // Modal handlers\n  const openCreateModal = () => {\n    setEditingSlot(null)\n    setModalMode('create')\n    setShowSlotModal(true)\n  }\n\n  const openEditModal = (slot: AvailabilitySlot) => {\n    setEditingSlot(slot)\n    setModalMode('edit')\n    setShowSlotModal(true)\n  }\n\n  const openBulkModal = () => {\n    setShowBulkModal(true)\n  }\n\n  // Slot management functions\n  const handleSaveSlot = async (slotData: any) => {\n    try {\n      const apiData = {\n        day_of_week: slotData.dayOfWeek,\n        start_time: slotData.startTime,\n        end_time: slotData.endTime,\n        max_appointments: slotData.maxAppointments,\n        is_available: slotData.isActive,\n        is_recurring: true, // All slots created through admin are recurring\n        notes: slotData.notes,\n        effective_from: new Date().toISOString().split('T')[0]\n      }\n\n      if (modalMode === 'create') {\n        await api.createAvailabilitySlot(apiData)\n      } else if (editingSlot) {\n        await api.updateAvailabilitySlot(editingSlot.id, apiData)\n      }\n\n      await loadAvailabilitySlots()\n      setShowSlotModal(false)\n    } catch (error) {\n      console.error('Error saving availability slot:', error)\n      throw error\n    }\n  }\n\n  const handleBulkSave = async (bulkData: any) => {\n    try {\n      // Create multiple slots for selected days\n      const promises = bulkData.selectedDays.map((dayOfWeek: number) => {\n        const apiData = {\n          day_of_week: dayOfWeek,\n          start_time: bulkData.startTime,\n          end_time: bulkData.endTime,\n          max_appointments: bulkData.maxAppointments,\n          is_available: true,\n          is_recurring: true,\n          notes: bulkData.notes,\n          effective_from: new Date().toISOString().split('T')[0]\n        }\n        return api.createAvailabilitySlot(apiData)\n      })\n\n      await Promise.all(promises)\n      await loadAvailabilitySlots()\n      setShowBulkModal(false)\n    } catch (error) {\n      console.error('Error creating bulk slots:', error)\n      throw error\n    }\n  }\n\n  const handleDeleteSlot = async (id: string) => {\n    if (!confirm('Are you sure you want to delete this availability slot?')) {\n      return\n    }\n\n    try {\n      setLoading(true)\n      await api.deleteAvailabilitySlot(id)\n      await loadAvailabilitySlots()\n    } catch (error) {\n      console.error('Error deleting availability slot:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const getRoleColor = (role: string) => {\n    switch (role) {\n      case 'admin':\n        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'\n      case 'doctor':\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'\n      case 'staff':\n        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'\n      default:\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'\n    }\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'active':\n        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'\n      case 'inactive':\n        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'\n      default:\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'\n    }\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Settings</h1>\n          <p className=\"text-muted-foreground\">\n            Manage your clinic settings, users, and system configuration\n          </p>\n        </div>\n        <Button onClick={handleSaveSettings} disabled={loading}>\n          {loading ? (\n            <RefreshCw className=\"mr-2 h-4 w-4 animate-spin\" />\n          ) : (\n            <Save className=\"mr-2 h-4 w-4\" />\n          )}\n          Save Changes\n        </Button>\n      </div>\n\n      {/* Settings Tabs */}\n      <div className=\"flex space-x-1 bg-muted p-1 rounded-lg w-fit\">\n        <Button\n          variant={activeTab === 'general' ? 'default' : 'ghost'}\n          size=\"sm\"\n          onClick={() => setActiveTab('general')}\n        >\n          <Settings className=\"mr-2 h-4 w-4\" />\n          General\n        </Button>\n        <Button\n          variant={activeTab === 'users' ? 'default' : 'ghost'}\n          size=\"sm\"\n          onClick={() => setActiveTab('users')}\n        >\n          <Shield className=\"mr-2 h-4 w-4\" />\n          Users\n        </Button>\n        <Button\n          variant={activeTab === 'clinic' ? 'default' : 'ghost'}\n          size=\"sm\"\n          onClick={() => setActiveTab('clinic')}\n        >\n          <Database className=\"mr-2 h-4 w-4\" />\n          Clinic Info\n        </Button>\n        <Button\n          variant={activeTab === 'availability' ? 'default' : 'ghost'}\n          size=\"sm\"\n          onClick={() => setActiveTab('availability')}\n        >\n          <Clock className=\"mr-2 h-4 w-4\" />\n          Availability\n        </Button>\n      </div>\n\n      {/* General Settings */}\n      {activeTab === 'general' && (\n        <div className=\"grid gap-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>System Configuration</CardTitle>\n              <CardDescription>\n                Basic system settings and preferences\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid gap-4 md:grid-cols-2\">\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">Time Zone</label>\n                  <Select defaultValue=\"asia/kolkata\">\n                    <SelectTrigger>\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"asia/kolkata\">Asia/Kolkata (IST)</SelectItem>\n                      <SelectItem value=\"utc\">UTC</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">Date Format</label>\n                  <Select defaultValue=\"dd/mm/yyyy\">\n                    <SelectTrigger>\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"dd/mm/yyyy\">DD/MM/YYYY</SelectItem>\n                      <SelectItem value=\"mm/dd/yyyy\">MM/DD/YYYY</SelectItem>\n                      <SelectItem value=\"yyyy-mm-dd\">YYYY-MM-DD</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n              </div>\n              <div className=\"grid gap-4 md:grid-cols-2\">\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">Currency</label>\n                  <Select defaultValue=\"inr\">\n                    <SelectTrigger>\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"inr\">Indian Rupee (₹)</SelectItem>\n                      <SelectItem value=\"usd\">US Dollar ($)</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">Language</label>\n                  <Select defaultValue=\"en\">\n                    <SelectTrigger>\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"en\">English</SelectItem>\n                      <SelectItem value=\"hi\">Hindi</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* Admin Users */}\n      {activeTab === 'users' && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Admin Users</CardTitle>\n            <CardDescription>\n              Manage admin users and their permissions\n            </CardDescription>\n            <Button size=\"sm\" className=\"w-fit\">\n              <Plus className=\"mr-2 h-4 w-4\" />\n              Add User\n            </Button>\n          </CardHeader>\n          <CardContent>\n            <Table>\n              <TableHeader>\n                <TableRow>\n                  <TableHead>User</TableHead>\n                  <TableHead>Role</TableHead>\n                  <TableHead>Status</TableHead>\n                  <TableHead>Last Login</TableHead>\n                  <TableHead className=\"text-right\">Actions</TableHead>\n                </TableRow>\n              </TableHeader>\n              <TableBody>\n                {adminUsers.map((user) => (\n                  <TableRow key={user.id}>\n                    <TableCell>\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-xs font-medium\">\n                          {user.name.charAt(0)}\n                        </div>\n                        <div>\n                          <div className=\"font-medium\">{user.name}</div>\n                          <div className=\"text-sm text-muted-foreground\">{user.email}</div>\n                        </div>\n                      </div>\n                    </TableCell>\n                    <TableCell>\n                      <Badge className={getRoleColor(user.role)}>\n                        {user.role}\n                      </Badge>\n                    </TableCell>\n                    <TableCell>\n                      <Badge className={getStatusColor(user.status)}>\n                        {user.status}\n                      </Badge>\n                    </TableCell>\n                    <TableCell>\n                      {new Date(user.lastLogin).toLocaleDateString()}\n                    </TableCell>\n                    <TableCell className=\"text-right\">\n                      <div className=\"flex items-center justify-end space-x-2\">\n                        <Button variant=\"ghost\" size=\"sm\">\n                          <Edit className=\"h-4 w-4\" />\n                        </Button>\n                        <Button variant=\"ghost\" size=\"sm\">\n                          <Trash2 className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Clinic Information */}\n      {activeTab === 'clinic' && (\n        <div className=\"grid gap-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Clinic Information</CardTitle>\n              <CardDescription>\n                Update your clinic&apos;s basic information and contact details\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">Clinic Name</label>\n                <Input\n                  value={clinicSettings.name}\n                  onChange={(e) => setClinicSettings({...clinicSettings, name: e.target.value})}\n                />\n              </div>\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">Address</label>\n                <Input\n                  value={clinicSettings.address}\n                  onChange={(e) => setClinicSettings({...clinicSettings, address: e.target.value})}\n                />\n              </div>\n              <div className=\"grid gap-4 md:grid-cols-2\">\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">Phone</label>\n                  <Input\n                    value={clinicSettings.phone}\n                    onChange={(e) => setClinicSettings({...clinicSettings, phone: e.target.value})}\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">Email</label>\n                  <Input\n                    value={clinicSettings.email}\n                    onChange={(e) => setClinicSettings({...clinicSettings, email: e.target.value})}\n                  />\n                </div>\n              </div>\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">Website</label>\n                <Input\n                  value={clinicSettings.website}\n                  onChange={(e) => setClinicSettings({...clinicSettings, website: e.target.value})}\n                />\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Working Hours */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Working Hours</CardTitle>\n              <CardDescription>\n                Set your clinic&apos;s operating hours for each day\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {Object.entries(clinicSettings.workingHours).map(([day, hours]) => (\n                  <div key={day} className=\"flex items-center space-x-4\">\n                    <div className=\"w-20 text-sm font-medium capitalize\">{day}</div>\n                    <div className=\"flex items-center space-x-2\">\n                      <Input\n                        type=\"time\"\n                        value={hours.open}\n                        className=\"w-24\"\n                        disabled={hours.closed}\n                      />\n                      <span className=\"text-muted-foreground\">to</span>\n                      <Input\n                        type=\"time\"\n                        value={hours.close}\n                        className=\"w-24\"\n                        disabled={hours.closed}\n                      />\n                      <Button\n                        variant={hours.closed ? \"outline\" : \"secondary\"}\n                        size=\"sm\"\n                      >\n                        {hours.closed ? \"Closed\" : \"Open\"}\n                      </Button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* Availability Slots */}\n      {activeTab === 'availability' && (\n        <Card>\n          <CardHeader>\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <CardTitle>Availability Slots</CardTitle>\n                <CardDescription>\n                  Manage recurring appointment time slots and capacity for each day of the week\n                </CardDescription>\n              </div>\n              <div className=\"flex gap-2\">\n                <Button size=\"sm\" variant=\"outline\" onClick={openBulkModal}>\n                  <Calendar className=\"mr-2 h-4 w-4\" />\n                  Bulk Create\n                </Button>\n                <Button size=\"sm\" onClick={openCreateModal}>\n                  <Plus className=\"mr-2 h-4 w-4\" />\n                  Add Slot\n                </Button>\n              </div>\n            </div>\n          </CardHeader>\n          <CardContent>\n            <Table>\n              <TableHeader>\n                <TableRow>\n                  <TableHead>Day/Date</TableHead>\n                  <TableHead>Time Slot</TableHead>\n                  <TableHead>Capacity</TableHead>\n                  <TableHead>Type</TableHead>\n                  <TableHead>Status</TableHead>\n                  <TableHead className=\"text-right\">Actions</TableHead>\n                </TableRow>\n              </TableHeader>\n              <TableBody>\n                {loading ? (\n                  <TableRow>\n                    <TableCell colSpan={5} className=\"text-center py-8\">\n                      <RefreshCw className=\"h-6 w-6 animate-spin mx-auto mb-2\" />\n                      <p>Loading availability slots...</p>\n                    </TableCell>\n                  </TableRow>\n                ) : availabilitySlots.length === 0 ? (\n                  <TableRow>\n                    <TableCell colSpan={6} className=\"text-center py-8\">\n                      <Calendar className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n                      <p className=\"text-muted-foreground\">No availability slots configured</p>\n                      <p className=\"text-sm text-muted-foreground\">Add your first slot to get started</p>\n                    </TableCell>\n                  </TableRow>\n                ) : (\n                  availabilitySlots.map((slot) => (\n                    <TableRow key={slot.id}>\n                      <TableCell className=\"font-medium\">\n                        <div className=\"flex flex-col\">\n                          <span>{slot.day}</span>\n                          {slot.isRecurring && (\n                            <span className=\"text-xs text-muted-foreground\">Every week</span>\n                          )}\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"flex items-center gap-2\">\n                          <Clock className=\"h-4 w-4 text-muted-foreground\" />\n                          {slot.startTime} - {slot.endTime}\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"flex flex-col\">\n                          <span className=\"font-medium\">{slot.currentAppointments || 0} / {slot.maxAppointments}</span>\n                          <span className=\"text-xs text-muted-foreground\">\n                            {slot.maxAppointments - (slot.currentAppointments || 0)} available\n                          </span>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <Badge variant=\"outline\" className=\"text-xs\">\n                          {slot.isRecurring ? 'Recurring' : 'Specific'}\n                        </Badge>\n                      </TableCell>\n                      <TableCell>\n                        <Badge variant={slot.isActive ? 'default' : 'secondary'}>\n                          {slot.isActive ? 'Active' : 'Inactive'}\n                        </Badge>\n                      </TableCell>\n                      <TableCell className=\"text-right\">\n                        <div className=\"flex items-center justify-end space-x-2\">\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={() => openEditModal(slot)}\n                            title=\"Edit slot\"\n                          >\n                            <Edit className=\"h-4 w-4\" />\n                          </Button>\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={() => handleDeleteSlot(slot.id)}\n                            title=\"Delete slot\"\n                          >\n                            <Trash2 className=\"h-4 w-4\" />\n                          </Button>\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ))\n                )}\n              </TableBody>\n            </Table>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Slot Management Modal */}\n      <SlotManagementModal\n        isOpen={showSlotModal}\n        onClose={() => setShowSlotModal(false)}\n        onSave={handleSaveSlot}\n        editingSlot={editingSlot}\n        mode={modalMode}\n      />\n\n      {/* Bulk Slot Creation Modal */}\n      <BulkSlotModal\n        isOpen={showBulkModal}\n        onClose={() => setShowBulkModal(false)}\n        onSave={handleBulkSave}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AACA;;;AAzCA;;;;;;;;;;;;AA8EO,SAAS;;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,yKAAQ,EAAiB;QACnE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,SAAS;QACT,aAAa;QACb,cAAc;YACZ,QAAQ;gBAAE,MAAM;gBAAS,OAAO;gBAAS,QAAQ;YAAM;YACvD,SAAS;gBAAE,MAAM;gBAAS,OAAO;gBAAS,QAAQ;YAAM;YACxD,WAAW;gBAAE,MAAM;gBAAS,OAAO;gBAAS,QAAQ;YAAM;YAC1D,UAAU;gBAAE,MAAM;gBAAS,OAAO;gBAAS,QAAQ;YAAM;YACzD,QAAQ;gBAAE,MAAM;gBAAS,OAAO;gBAAS,QAAQ;YAAM;YACvD,UAAU;gBAAE,MAAM;gBAAS,OAAO;gBAAS,QAAQ;YAAM;YACzD,QAAQ;gBAAE,MAAM;gBAAS,OAAO;gBAAS,QAAQ;YAAK;QACxD;IACF;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAc;QACxD;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;QACnC;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ;YACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,WAAW;QACvD;KACD;IAED,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,IAAA,yKAAQ,EAAqB,EAAE;IACjF,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;IAE3C,eAAe;IACf,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAC;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAC;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAA0B;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAoB;IAE9D,6CAA6C;IAC7C,IAAA,0KAAS;kCAAC;YACR,IAAI,cAAc,gBAAgB;gBAChC;YACF;QACF;iCAAG;QAAC;KAAU;IAEd,MAAM,wBAAwB;QAC5B,IAAI;YACF,WAAW;YACX,MAAM,QAAQ,MAAM,qIAAG,CAAC,oBAAoB;YAE5C,+CAA+C;YAC/C,MAAM,mBAAmB,MAAM,GAAG,CAAC,CAAA;gBACjC,MAAM,WAAW;oBAAC;oBAAU;oBAAU;oBAAW;oBAAa;oBAAY;oBAAU;iBAAW;gBAE/F,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,KAAK,KAAK,YAAY,GAClB,QAAQ,CAAC,KAAK,WAAW,CAAC,IAAI,YAC9B,KAAK,IAAI,GAAG,IAAI,KAAK,KAAK,IAAI,EAAE,kBAAkB,CAAC,SAAS;wBAAE,SAAS;oBAAO,KAAK;oBACvF,WAAW,KAAK,WAAW;oBAC3B,WAAW,KAAK,UAAU;oBAC1B,SAAS,KAAK,QAAQ;oBACtB,iBAAiB,KAAK,gBAAgB;oBACtC,UAAU,KAAK,YAAY;oBAC3B,aAAa,KAAK,YAAY;oBAC9B,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,qBAAqB,KAAK,oBAAoB,IAAI;gBACpD;YACF;YAEA,2EAA2E;YAC3E,iBAAiB,IAAI,CAAC,CAAC,GAAG;gBACxB,IAAI,EAAE,WAAW,IAAI,EAAE,WAAW,EAAE;oBAClC,OAAO,CAAC,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE,SAAS,IAAI,CAAC;gBAC/C;gBACA,IAAI,EAAE,WAAW,IAAI,CAAC,EAAE,WAAW,EAAE,OAAO,CAAC;gBAC7C,IAAI,CAAC,EAAE,WAAW,IAAI,EAAE,WAAW,EAAE,OAAO;gBAC5C,OAAO,CAAC,EAAE,IAAI,IAAI,EAAE,EAAE,aAAa,CAAC,EAAE,IAAI,IAAI;YAChD;YAEA,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB;QACzB,WAAW;QACX,IAAI;YACF,qDAAqD;YACrD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,QAAQ,GAAG,CAAC,mBAAmB;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,iBAAiB;IACjB,MAAM,kBAAkB;QACtB,eAAe;QACf,aAAa;QACb,iBAAiB;IACnB;IAEA,MAAM,gBAAgB,CAAC;QACrB,eAAe;QACf,aAAa;QACb,iBAAiB;IACnB;IAEA,MAAM,gBAAgB;QACpB,iBAAiB;IACnB;IAEA,4BAA4B;IAC5B,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,UAAU;gBACd,aAAa,SAAS,SAAS;gBAC/B,YAAY,SAAS,SAAS;gBAC9B,UAAU,SAAS,OAAO;gBAC1B,kBAAkB,SAAS,eAAe;gBAC1C,cAAc,SAAS,QAAQ;gBAC/B,cAAc;gBACd,OAAO,SAAS,KAAK;gBACrB,gBAAgB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACxD;YAEA,IAAI,cAAc,UAAU;gBAC1B,MAAM,qIAAG,CAAC,sBAAsB,CAAC;YACnC,OAAO,IAAI,aAAa;gBACtB,MAAM,qIAAG,CAAC,sBAAsB,CAAC,YAAY,EAAE,EAAE;YACnD;YAEA,MAAM;YACN,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,0CAA0C;YAC1C,MAAM,WAAW,SAAS,YAAY,CAAC,GAAG,CAAC,CAAC;gBAC1C,MAAM,UAAU;oBACd,aAAa;oBACb,YAAY,SAAS,SAAS;oBAC9B,UAAU,SAAS,OAAO;oBAC1B,kBAAkB,SAAS,eAAe;oBAC1C,cAAc;oBACd,cAAc;oBACd,OAAO,SAAS,KAAK;oBACrB,gBAAgB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBACxD;gBACA,OAAO,qIAAG,CAAC,sBAAsB,CAAC;YACpC;YAEA,MAAM,QAAQ,GAAG,CAAC;YAClB,MAAM;YACN,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,QAAQ,4DAA4D;YACvE;QACF;QAEA,IAAI;YACF,WAAW;YACX,MAAM,qIAAG,CAAC,sBAAsB,CAAC;YACjC,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6LAAC,+IAAM;wBAAC,SAAS;wBAAoB,UAAU;;4BAC5C,wBACC,6LAAC,gOAAS;gCAAC,WAAU;;;;;qDAErB,6LAAC,6MAAI;gCAAC,WAAU;;;;;;4BAChB;;;;;;;;;;;;;0BAMN,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+IAAM;wBACL,SAAS,cAAc,YAAY,YAAY;wBAC/C,MAAK;wBACL,SAAS,IAAM,aAAa;;0CAE5B,6LAAC,yNAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAGvC,6LAAC,+IAAM;wBACL,SAAS,cAAc,UAAU,YAAY;wBAC7C,MAAK;wBACL,SAAS,IAAM,aAAa;;0CAE5B,6LAAC,mNAAM;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAGrC,6LAAC,+IAAM;wBACL,SAAS,cAAc,WAAW,YAAY;wBAC9C,MAAK;wBACL,SAAS,IAAM,aAAa;;0CAE5B,6LAAC,yNAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAGvC,6LAAC,+IAAM;wBACL,SAAS,cAAc,iBAAiB,YAAY;wBACpD,MAAK;wBACL,SAAS,IAAM,aAAa;;0CAE5B,6LAAC,gNAAK;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAMrC,cAAc,2BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,2IAAI;;sCACH,6LAAC,iJAAU;;8CACT,6LAAC,gJAAS;8CAAC;;;;;;8CACX,6LAAC,sJAAe;8CAAC;;;;;;;;;;;;sCAInB,6LAAC,kJAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;8DAAsB;;;;;;8DACvC,6LAAC,+IAAM;oDAAC,cAAa;;sEACnB,6LAAC,sJAAa;sEACZ,cAAA,6LAAC,oJAAW;;;;;;;;;;sEAEd,6LAAC,sJAAa;;8EACZ,6LAAC,mJAAU;oEAAC,OAAM;8EAAe;;;;;;8EACjC,6LAAC,mJAAU;oEAAC,OAAM;8EAAM;;;;;;;;;;;;;;;;;;;;;;;;sDAI9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;8DAAsB;;;;;;8DACvC,6LAAC,+IAAM;oDAAC,cAAa;;sEACnB,6LAAC,sJAAa;sEACZ,cAAA,6LAAC,oJAAW;;;;;;;;;;sEAEd,6LAAC,sJAAa;;8EACZ,6LAAC,mJAAU;oEAAC,OAAM;8EAAa;;;;;;8EAC/B,6LAAC,mJAAU;oEAAC,OAAM;8EAAa;;;;;;8EAC/B,6LAAC,mJAAU;oEAAC,OAAM;8EAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAKvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;8DAAsB;;;;;;8DACvC,6LAAC,+IAAM;oDAAC,cAAa;;sEACnB,6LAAC,sJAAa;sEACZ,cAAA,6LAAC,oJAAW;;;;;;;;;;sEAEd,6LAAC,sJAAa;;8EACZ,6LAAC,mJAAU;oEAAC,OAAM;8EAAM;;;;;;8EACxB,6LAAC,mJAAU;oEAAC,OAAM;8EAAM;;;;;;;;;;;;;;;;;;;;;;;;sDAI9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;8DAAsB;;;;;;8DACvC,6LAAC,+IAAM;oDAAC,cAAa;;sEACnB,6LAAC,sJAAa;sEACZ,cAAA,6LAAC,oJAAW;;;;;;;;;;sEAEd,6LAAC,sJAAa;;8EACZ,6LAAC,mJAAU;oEAAC,OAAM;8EAAK;;;;;;8EACvB,6LAAC,mJAAU;oEAAC,OAAM;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWtC,cAAc,yBACb,6LAAC,2IAAI;;kCACH,6LAAC,iJAAU;;0CACT,6LAAC,gJAAS;0CAAC;;;;;;0CACX,6LAAC,sJAAe;0CAAC;;;;;;0CAGjB,6LAAC,+IAAM;gCAAC,MAAK;gCAAK,WAAU;;kDAC1B,6LAAC,6MAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAIrC,6LAAC,kJAAW;kCACV,cAAA,6LAAC,6IAAK;;8CACJ,6LAAC,mJAAW;8CACV,cAAA,6LAAC,gJAAQ;;0DACP,6LAAC,iJAAS;0DAAC;;;;;;0DACX,6LAAC,iJAAS;0DAAC;;;;;;0DACX,6LAAC,iJAAS;0DAAC;;;;;;0DACX,6LAAC,iJAAS;0DAAC;;;;;;0DACX,6LAAC,iJAAS;gDAAC,WAAU;0DAAa;;;;;;;;;;;;;;;;;8CAGtC,6LAAC,iJAAS;8CACP,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,gJAAQ;;8DACP,6LAAC,iJAAS;8DACR,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACZ,KAAK,IAAI,CAAC,MAAM,CAAC;;;;;;0EAEpB,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;kFAAe,KAAK,IAAI;;;;;;kFACvC,6LAAC;wEAAI,WAAU;kFAAiC,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;8DAIhE,6LAAC,iJAAS;8DACR,cAAA,6LAAC,6IAAK;wDAAC,WAAW,aAAa,KAAK,IAAI;kEACrC,KAAK,IAAI;;;;;;;;;;;8DAGd,6LAAC,iJAAS;8DACR,cAAA,6LAAC,6IAAK;wDAAC,WAAW,eAAe,KAAK,MAAM;kEACzC,KAAK,MAAM;;;;;;;;;;;8DAGhB,6LAAC,iJAAS;8DACP,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;8DAE9C,6LAAC,iJAAS;oDAAC,WAAU;8DACnB,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+IAAM;gEAAC,SAAQ;gEAAQ,MAAK;0EAC3B,cAAA,6LAAC,sNAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,6LAAC,+IAAM;gEAAC,SAAQ;gEAAQ,MAAK;0EAC3B,cAAA,6LAAC,uNAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CA/BX,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;YA4CjC,cAAc,0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2IAAI;;0CACH,6LAAC,iJAAU;;kDACT,6LAAC,gJAAS;kDAAC;;;;;;kDACX,6LAAC,sJAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,kJAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,6LAAC,6IAAK;gDACJ,OAAO,eAAe,IAAI;gDAC1B,UAAU,CAAC,IAAM,kBAAkB;wDAAC,GAAG,cAAc;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAA;;;;;;;;;;;;kDAG/E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,6LAAC,6IAAK;gDACJ,OAAO,eAAe,OAAO;gDAC7B,UAAU,CAAC,IAAM,kBAAkB;wDAAC,GAAG,cAAc;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAA;;;;;;;;;;;;kDAGlF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,6LAAC,6IAAK;wDACJ,OAAO,eAAe,KAAK;wDAC3B,UAAU,CAAC,IAAM,kBAAkB;gEAAC,GAAG,cAAc;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAA;;;;;;;;;;;;0DAGhF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,6LAAC,6IAAK;wDACJ,OAAO,eAAe,KAAK;wDAC3B,UAAU,CAAC,IAAM,kBAAkB;gEAAC,GAAG,cAAc;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAA;;;;;;;;;;;;;;;;;;kDAIlF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,6LAAC,6IAAK;gDACJ,OAAO,eAAe,OAAO;gDAC7B,UAAU,CAAC,IAAM,kBAAkB;wDAAC,GAAG,cAAc;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAA;;;;;;;;;;;;;;;;;;;;;;;;kCAOtF,6LAAC,2IAAI;;0CACH,6LAAC,iJAAU;;kDACT,6LAAC,gJAAS;kDAAC;;;;;;kDACX,6LAAC,sJAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,kJAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CACZ,OAAO,OAAO,CAAC,eAAe,YAAY,EAAE,GAAG,CAAC;4CAAC,CAAC,KAAK,MAAM;6DAC5D,6LAAC;4CAAc,WAAU;;8DACvB,6LAAC;oDAAI,WAAU;8DAAuC;;;;;;8DACtD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6IAAK;4DACJ,MAAK;4DACL,OAAO,MAAM,IAAI;4DACjB,WAAU;4DACV,UAAU,MAAM,MAAM;;;;;;sEAExB,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,6LAAC,6IAAK;4DACJ,MAAK;4DACL,OAAO,MAAM,KAAK;4DAClB,WAAU;4DACV,UAAU,MAAM,MAAM;;;;;;sEAExB,6LAAC,+IAAM;4DACL,SAAS,MAAM,MAAM,GAAG,YAAY;4DACpC,MAAK;sEAEJ,MAAM,MAAM,GAAG,WAAW;;;;;;;;;;;;;2CApBvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAgCrB,cAAc,gCACb,6LAAC,2IAAI;;kCACH,6LAAC,iJAAU;kCACT,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC,gJAAS;sDAAC;;;;;;sDACX,6LAAC,sJAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+IAAM;4CAAC,MAAK;4CAAK,SAAQ;4CAAU,SAAS;;8DAC3C,6LAAC,yNAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,6LAAC,+IAAM;4CAAC,MAAK;4CAAK,SAAS;;8DACzB,6LAAC,6MAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;kCAMzC,6LAAC,kJAAW;kCACV,cAAA,6LAAC,6IAAK;;8CACJ,6LAAC,mJAAW;8CACV,cAAA,6LAAC,gJAAQ;;0DACP,6LAAC,iJAAS;0DAAC;;;;;;0DACX,6LAAC,iJAAS;0DAAC;;;;;;0DACX,6LAAC,iJAAS;0DAAC;;;;;;0DACX,6LAAC,iJAAS;0DAAC;;;;;;0DACX,6LAAC,iJAAS;0DAAC;;;;;;0DACX,6LAAC,iJAAS;gDAAC,WAAU;0DAAa;;;;;;;;;;;;;;;;;8CAGtC,6LAAC,iJAAS;8CACP,wBACC,6LAAC,gJAAQ;kDACP,cAAA,6LAAC,iJAAS;4CAAC,SAAS;4CAAG,WAAU;;8DAC/B,6LAAC,gOAAS;oDAAC,WAAU;;;;;;8DACrB,6LAAC;8DAAE;;;;;;;;;;;;;;;;+CAGL,kBAAkB,MAAM,KAAK,kBAC/B,6LAAC,gJAAQ;kDACP,cAAA,6LAAC,iJAAS;4CAAC,SAAS;4CAAG,WAAU;;8DAC/B,6LAAC,yNAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;+CAIjD,kBAAkB,GAAG,CAAC,CAAC,qBACrB,6LAAC,gJAAQ;;8DACP,6LAAC,iJAAS;oDAAC,WAAU;8DACnB,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAM,KAAK,GAAG;;;;;;4DACd,KAAK,WAAW,kBACf,6LAAC;gEAAK,WAAU;0EAAgC;;;;;;;;;;;;;;;;;8DAItD,6LAAC,iJAAS;8DACR,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,gNAAK;gEAAC,WAAU;;;;;;4DAChB,KAAK,SAAS;4DAAC;4DAAI,KAAK,OAAO;;;;;;;;;;;;8DAGpC,6LAAC,iJAAS;8DACR,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEAAe,KAAK,mBAAmB,IAAI;oEAAE;oEAAI,KAAK,eAAe;;;;;;;0EACrF,6LAAC;gEAAK,WAAU;;oEACb,KAAK,eAAe,GAAG,CAAC,KAAK,mBAAmB,IAAI,CAAC;oEAAE;;;;;;;;;;;;;;;;;;8DAI9D,6LAAC,iJAAS;8DACR,cAAA,6LAAC,6IAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAChC,KAAK,WAAW,GAAG,cAAc;;;;;;;;;;;8DAGtC,6LAAC,iJAAS;8DACR,cAAA,6LAAC,6IAAK;wDAAC,SAAS,KAAK,QAAQ,GAAG,YAAY;kEACzC,KAAK,QAAQ,GAAG,WAAW;;;;;;;;;;;8DAGhC,6LAAC,iJAAS;oDAAC,WAAU;8DACnB,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+IAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,cAAc;gEAC7B,OAAM;0EAEN,cAAA,6LAAC,sNAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,6LAAC,+IAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,iBAAiB,KAAK,EAAE;gEACvC,OAAM;0EAEN,cAAA,6LAAC,uNAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CAjDX,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA+DpC,6LAAC,uLAAmB;gBAClB,QAAQ;gBACR,SAAS,IAAM,iBAAiB;gBAChC,QAAQ;gBACR,aAAa;gBACb,MAAM;;;;;;0BAIR,6LAAC,2KAAa;gBACZ,QAAQ;gBACR,SAAS,IAAM,iBAAiB;gBAChC,QAAQ;;;;;;;;;;;;AAIhB;GAloBgB;KAAA", "debugId": null}}]}