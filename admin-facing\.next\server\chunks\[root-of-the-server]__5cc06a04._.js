module.exports = [
"[project]/.next-internal/server/app/api/availability-slots/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/stream [external] (stream, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}),
"[externals]/http [external] (http, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}),
"[externals]/url [external] (url, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}),
"[externals]/punycode [external] (punycode, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}),
"[externals]/https [external] (https, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}),
"[externals]/zlib [external] (zlib, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}),
"[project]/src/lib/supabase-server.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "api",
    ()=>api,
    "createServerSupabaseClient",
    ()=>createServerSupabaseClient,
    "supabaseAdmin",
    ()=>supabaseAdmin
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createServerClient.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
;
;
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://yuyruqdqlkecsnschziv.supabase.co");
const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl1eXJ1cWRxbGtlY3Nuc2Noeml2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg3NzY5NTEsImV4cCI6MjA3NDM1Mjk1MX0.vCcS1fALVF4yWdykoWNqaLs2UQhjpOaDbA8G4GYJIxc");
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabaseAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseServiceRoleKey, {
    auth: {
        autoRefreshToken: false,
        persistSession: false
    }
});
const createServerSupabaseClient = async ()=>{
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createServerClient"])(supabaseUrl, supabaseAnonKey, {
        cookies: {
            get (name) {
                return cookieStore.get(name)?.value;
            },
            set (name, value, options) {
                cookieStore.set({
                    name,
                    value,
                    ...options
                });
            },
            remove (name, options) {
                cookieStore.set({
                    name,
                    value: '',
                    ...options
                });
            }
        }
    });
};
const api = {
    // Patients
    async getPatients () {
        // Get all user profiles
        const { data: profiles, error: profilesError } = await supabaseAdmin.from('user_profiles').select('*').order('created_at', {
            ascending: false
        });
        if (profilesError) throw profilesError;
        // For each profile, get their appointment data and counts
        const patientsWithAppointmentData = await Promise.all(profiles.map(async (profile)=>{
            // Get all appointments for this patient
            const { data: appointments, error: appointmentsError } = await supabaseAdmin.from('appointments').select('*').eq('user_id', profile.id).order('created_at', {
                ascending: false
            });
            if (appointmentsError) {
                console.error('Error fetching appointments for patient:', profile.id, appointmentsError);
            }
            const patientAppointments = appointments || [];
            // Get the latest appointment for medical info
            const latestAppointment = patientAppointments[0];
            // Calculate appointment statistics
            const totalAppointments = patientAppointments.length;
            const completedAppointments = patientAppointments.filter((apt)=>apt.status === 'completed').length;
            const pendingAppointments = patientAppointments.filter((apt)=>apt.status === 'pending').length;
            const confirmedAppointments = patientAppointments.filter((apt)=>apt.status === 'confirmed').length;
            const cancelledAppointments = patientAppointments.filter((apt)=>apt.status === 'cancelled').length;
            // Find the most recent completed appointment for last visit date
            const lastCompletedAppointment = patientAppointments.find((apt)=>apt.status === 'completed');
            const lastVisitDate = lastCompletedAppointment?.preferred_date || latestAppointment?.preferred_date;
            return {
                ...profile,
                // Medical information from latest appointment
                medical_history: latestAppointment?.medical_history || profile.medical_history,
                allergies: latestAppointment?.allergies || profile.allergies,
                current_medications: latestAppointment?.current_medications,
                previous_dental_work: latestAppointment?.previous_dental_work,
                dental_concerns: latestAppointment?.dental_concerns,
                emergency_contact: latestAppointment?.emergency_contact_name || profile.emergency_contact,
                emergency_contact_phone: latestAppointment?.emergency_contact_phone,
                emergency_contact_relationship: latestAppointment?.emergency_contact_relationship,
                has_insurance: latestAppointment?.has_insurance,
                insurance_provider: latestAppointment?.insurance_provider,
                insurance_policy_number: latestAppointment?.insurance_policy_number,
                // Appointment statistics
                total_appointments: totalAppointments,
                completed_appointments: completedAppointments,
                pending_appointments: pendingAppointments,
                confirmed_appointments: confirmedAppointments,
                cancelled_appointments: cancelledAppointments,
                last_visit: lastVisitDate,
                // Additional patient info
                date_of_birth: latestAppointment?.date_of_birth || profile.date_of_birth,
                address: latestAppointment?.address || profile.address,
                phone: latestAppointment?.phone || profile.phone
            };
        }));
        return patientsWithAppointmentData;
    },
    async getPatient (id) {
        // Get user profile
        const { data: profile, error: profileError } = await supabaseAdmin.from('user_profiles').select('*').eq('id', id).single();
        if (profileError) throw profileError;
        // Get latest appointment with medical information
        const { data: latestAppointment } = await supabaseAdmin.from('appointments').select('*').eq('user_id', id).order('created_at', {
            ascending: false
        }).limit(1).single();
        // Merge profile with medical information from latest appointment
        const patientData = {
            ...profile,
            medical_history: latestAppointment?.medical_history || profile.medical_history,
            allergies: latestAppointment?.allergies || profile.allergies,
            current_medications: latestAppointment?.current_medications || profile.current_medications,
            previous_dental_work: latestAppointment?.previous_dental_work || profile.previous_dental_work,
            dental_concerns: latestAppointment?.dental_concerns || profile.dental_concerns,
            emergency_contact: latestAppointment?.emergency_contact_name || profile.emergency_contact,
            emergency_contact_phone: latestAppointment?.emergency_contact_phone || profile.emergency_contact_phone,
            emergency_contact_relationship: latestAppointment?.emergency_contact_relationship || profile.emergency_contact_relationship,
            has_insurance: latestAppointment?.has_insurance || profile.has_insurance,
            insurance_provider: latestAppointment?.insurance_provider || profile.insurance_provider,
            insurance_policy_number: latestAppointment?.insurance_policy_number || profile.insurance_policy_number,
            last_visit: latestAppointment?.preferred_date || profile.last_visit
        };
        return patientData;
    },
    async createPatient (patient) {
        const { data, error } = await supabaseAdmin.from('user_profiles').insert(patient).select().single();
        if (error) throw error;
        return data;
    },
    async updatePatient (id, updates) {
        const { data, error } = await supabaseAdmin.from('user_profiles').update({
            ...updates,
            updated_at: new Date().toISOString()
        }).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    async getPatientAppointments (patientId) {
        const { data, error } = await supabaseAdmin.from('appointments').select('*').eq('user_id', patientId).order('created_at', {
            ascending: false
        });
        if (error) throw error;
        return data;
    },
    // Appointments
    async getAppointments () {
        const { data, error } = await supabaseAdmin.from('appointments').select('*').order('preferred_date', {
            ascending: true
        });
        if (error) throw error;
        return data;
    },
    async getPendingAppointments () {
        const { data, error } = await supabaseAdmin.from('appointments').select('*').eq('status', 'pending').order('created_at', {
            ascending: false
        });
        if (error) throw error;
        return data;
    },
    async getAppointment (id) {
        const { data, error } = await supabaseAdmin.from('appointments').select('*').eq('id', id).single();
        if (error) throw error;
        return data;
    },
    async createAppointment (appointment) {
        const { data, error } = await supabaseAdmin.from('appointments').insert(appointment).select().single();
        if (error) throw error;
        return data;
    },
    async updateAppointment (id, updates) {
        const { data, error } = await supabaseAdmin.from('appointments').update({
            ...updates,
            updated_at: new Date().toISOString()
        }).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    // Services
    async getServices () {
        const { data, error } = await supabaseAdmin.from('services').select('*').eq('is_active', true).order('title', {
            ascending: true
        });
        if (error) throw error;
        return data;
    },
    async getAllServices () {
        const { data, error } = await supabaseAdmin.from('services').select('*').order('title', {
            ascending: true
        });
        if (error) throw error;
        return data;
    },
    async getService (id) {
        const { data, error } = await supabaseAdmin.from('services').select('*').eq('id', id).single();
        if (error) throw error;
        return data;
    },
    async createService (service) {
        const { data, error } = await supabaseAdmin.from('services').insert(service).select().single();
        if (error) throw error;
        return data;
    },
    async updateService (id, updates) {
        const { data, error } = await supabaseAdmin.from('services').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    // Contact Forms
    async getContactForms () {
        const { data, error } = await supabaseAdmin.from('contact_forms').select('*').order('created_at', {
            ascending: false
        });
        if (error) throw error;
        return data;
    },
    async updateContactForm (id, updates) {
        const { data, error } = await supabaseAdmin.from('contact_forms').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    // Medical Records
    async getMedicalRecords (patientId) {
        // Get traditional medical records
        let medicalRecordsQuery = supabaseAdmin.from('medical_records').select(`
        *,
        user_profiles!patient_id (
          id,
          full_name,
          email,
          phone
        ),
        appointments (
          id,
          service,
          preferred_date,
          preferred_time
        )
      `).order('created_at', {
            ascending: false
        });
        if (patientId) {
            medicalRecordsQuery = medicalRecordsQuery.eq('patient_id', patientId);
        }
        // Get appointments with medical information
        let appointmentsQuery = supabaseAdmin.from('appointments').select(`
        id,
        created_at,
        user_id,
        name,
        email,
        phone,
        service,
        preferred_date,
        preferred_time,
        status,
        medical_history,
        allergies,
        current_medications,
        previous_dental_work,
        dental_concerns,
        emergency_contact_name,
        emergency_contact_phone,
        emergency_contact_relationship,
        has_insurance,
        insurance_provider,
        insurance_policy_number
      `).not('medical_history', 'is', null).order('created_at', {
            ascending: false
        });
        if (patientId) {
            appointmentsQuery = appointmentsQuery.eq('user_id', patientId);
        }
        const [medicalRecordsResult, appointmentsResult] = await Promise.all([
            medicalRecordsQuery,
            appointmentsQuery
        ]);
        if (medicalRecordsResult.error) throw medicalRecordsResult.error;
        if (appointmentsResult.error) throw appointmentsResult.error;
        // Combine and format the data
        const medicalRecords = medicalRecordsResult.data || [];
        const appointmentRecords = (appointmentsResult.data || []).map((appointment)=>({
                id: appointment.id,
                created_at: appointment.created_at,
                patient_id: appointment.user_id,
                record_type: 'appointment_intake',
                title: `Medical Intake - ${appointment.service}`,
                description: `Medical information collected during appointment booking for ${appointment.service}`,
                content: {
                    service: appointment.service,
                    appointment_date: appointment.preferred_date,
                    appointment_time: appointment.preferred_time,
                    medical_history: appointment.medical_history,
                    allergies: appointment.allergies,
                    current_medications: appointment.current_medications,
                    previous_dental_work: appointment.previous_dental_work,
                    dental_concerns: appointment.dental_concerns,
                    emergency_contact: {
                        name: appointment.emergency_contact_name,
                        phone: appointment.emergency_contact_phone,
                        relationship: appointment.emergency_contact_relationship
                    },
                    insurance: {
                        has_insurance: appointment.has_insurance,
                        provider: appointment.insurance_provider,
                        policy_number: appointment.insurance_policy_number
                    }
                },
                user_profiles: {
                    id: appointment.user_id,
                    full_name: appointment.name,
                    email: appointment.email,
                    phone: appointment.phone
                },
                appointments: null
            }));
        // Combine and sort all records
        const allRecords = [
            ...medicalRecords,
            ...appointmentRecords
        ].sort((a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        return allRecords;
    },
    async getMedicalRecord (id) {
        const { data, error } = await supabaseAdmin.from('medical_records').select(`
        *,
        user_profiles!patient_id (
          id,
          full_name,
          email,
          phone,
          date_of_birth,
          medical_history,
          allergies
        ),
        appointments (
          id,
          service,
          preferred_date,
          preferred_time
        )
      `).eq('id', id).single();
        if (error) throw error;
        return data;
    },
    async createMedicalRecord (record) {
        const { data, error } = await supabaseAdmin.from('medical_records').insert(record).select().single();
        if (error) throw error;
        return data;
    },
    async updateMedicalRecord (id, updates) {
        const { data, error } = await supabaseAdmin.from('medical_records').update({
            ...updates,
            updated_at: new Date().toISOString()
        }).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    // Availability Slots
    async getAvailabilitySlots () {
        const { data, error } = await supabaseAdmin.from('availability_slots').select('*').order('date', {
            ascending: true
        }).order('start_time', {
            ascending: true
        });
        if (error) throw error;
        return data;
    },
    async createAvailabilitySlot (slot) {
        const { data, error } = await supabaseAdmin.from('availability_slots').insert({
            date: slot.date,
            start_time: slot.start_time,
            end_time: slot.end_time,
            is_available: slot.is_available ?? true,
            max_appointments: slot.max_appointments ?? 1,
            current_appointments: 0,
            notes: slot.notes
        }).select().single();
        if (error) throw error;
        return data;
    },
    async updateAvailabilitySlot (id, updates) {
        const { data, error } = await supabaseAdmin.from('availability_slots').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    async deleteAvailabilitySlot (id) {
        const { error } = await supabaseAdmin.from('availability_slots').delete().eq('id', id);
        if (error) throw error;
        return {
            success: true
        };
    },
    async checkSlotAvailability (date, time) {
        // Get availability slots for the date
        const { data: slots, error: slotsError } = await supabaseAdmin.from('availability_slots').select('*').eq('date', date).eq('is_available', true);
        if (slotsError) throw slotsError;
        // Get existing appointments for the date
        const { data: appointments, error: appointmentsError } = await supabaseAdmin.from('appointments').select('preferred_time, status').eq('preferred_date', date).in('status', [
            'pending',
            'confirmed',
            'in_progress'
        ]);
        if (appointmentsError) throw appointmentsError;
        if (time) {
            // Check specific time slot
            const timeSlot = slots.find((slot)=>{
                return time >= slot.start_time && time <= slot.end_time;
            });
            if (!timeSlot) {
                return {
                    available: false,
                    reason: 'No availability slot configured for this time'
                };
            }
            const appointmentsAtTime = appointments.filter((apt)=>apt.preferred_time === time).length;
            const available = appointmentsAtTime < timeSlot.max_appointments;
            return {
                available,
                reason: available ? null : 'Time slot is fully booked',
                maxAppointments: timeSlot.max_appointments,
                currentAppointments: appointmentsAtTime
            };
        }
        // Return all slots with availability info
        const slotsWithAvailability = slots.map((slot)=>{
            const appointmentsInSlot = appointments.filter((apt)=>{
                return apt.preferred_time >= slot.start_time && apt.preferred_time <= slot.end_time;
            }).length;
            return {
                ...slot,
                current_appointments: appointmentsInSlot,
                available: appointmentsInSlot < slot.max_appointments
            };
        });
        return {
            date,
            slots: slotsWithAvailability
        };
    },
    // Analytics
    async getDashboardStats () {
        const [patientsResult, appointmentsResult, servicesResult, contactFormsResult] = await Promise.all([
            supabaseAdmin.from('user_profiles').select('id', {
                count: 'exact'
            }),
            supabaseAdmin.from('appointments').select('id, status', {
                count: 'exact'
            }),
            supabaseAdmin.from('services').select('id', {
                count: 'exact'
            }).eq('is_active', true),
            supabaseAdmin.from('contact_forms').select('id', {
                count: 'exact'
            }).eq('status', 'new')
        ]);
        const totalPatients = patientsResult.count || 0;
        const totalAppointments = appointmentsResult.count || 0;
        const totalServices = servicesResult.count || 0;
        const newMessages = contactFormsResult.count || 0;
        // Calculate appointment stats
        const appointments = appointmentsResult.data || [];
        const pendingAppointments = appointments.filter((a)=>a.status === 'pending').length;
        const confirmedAppointments = appointments.filter((a)=>a.status === 'confirmed').length;
        const completedAppointments = appointments.filter((a)=>a.status === 'completed').length;
        const inProgressAppointments = appointments.filter((a)=>a.status === 'in_progress').length;
        return {
            totalPatients,
            totalAppointments,
            totalServices,
            newMessages,
            pendingAppointments,
            confirmedAppointments,
            completedAppointments,
            inProgressAppointments
        };
    }
};
}),
"[project]/src/app/api/availability-slots/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>GET,
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase-server.ts [app-route] (ecmascript)");
;
;
async function GET() {
    try {
        const { data: slots, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabaseAdmin"].from('availability_slots').select('*').order('is_recurring', {
            ascending: false
        }) // Recurring slots first
        .order('day_of_week', {
            ascending: true
        }).order('date', {
            ascending: true
        }).order('start_time', {
            ascending: true
        });
        if (error) throw error;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(slots);
    } catch (error) {
        console.error('Error fetching availability slots:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to fetch availability slots'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const { data: slot, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabaseAdmin"].from('availability_slots').insert({
            date: body.date,
            start_time: body.start_time,
            end_time: body.end_time,
            is_available: body.is_available ?? true,
            max_appointments: body.max_appointments ?? 1,
            current_appointments: 0,
            notes: body.notes
        }).select().single();
        if (error) throw error;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(slot);
    } catch (error) {
        console.error('Error creating availability slot:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to create availability slot'
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__5cc06a04._.js.map