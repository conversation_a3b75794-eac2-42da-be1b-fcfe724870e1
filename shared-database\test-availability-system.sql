-- =====================================================
-- TEST AVAILABILITY SYSTEM
-- =====================================================
-- This script tests the enhanced availability slots system
-- Run this after setting up the enhanced schema

-- Test 1: Verify recurring slots were created
SELECT 
    'Test 1: Recurring Slots Created' as test_name,
    COUNT(*) as recurring_slots_count,
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS' 
        ELSE 'FAIL' 
    END as status
FROM availability_slots 
WHERE is_recurring = true;

-- Test 2: Check day-of-week distribution
SELECT 
    'Test 2: Day Distribution' as test_name,
    day_of_week,
    CASE day_of_week
        WHEN 0 THEN 'Sunday'
        WHEN 1 THEN 'Monday'
        WHEN 2 THEN 'Tuesday'
        WHEN 3 THEN 'Wednesday'
        WHEN 4 THEN 'Thursday'
        WHEN 5 THEN 'Friday'
        WHEN 6 THEN 'Saturday'
    END as day_name,
    COUNT(*) as slot_count
FROM availability_slots 
WHERE is_recurring = true
GROUP BY day_of_week
ORDER BY day_of_week;

-- Test 3: Test the get_available_slots_for_date function for today
SELECT 
    'Test 3: Available Slots for Today' as test_name,
    slot_date,
    start_time,
    end_time,
    max_appointments,
    current_appointments,
    is_available,
    is_from_template
FROM get_available_slots_for_date(CURRENT_DATE)
ORDER BY start_time;

-- Test 4: Test for a specific weekday (next Monday)
SELECT 
    'Test 4: Available Slots for Next Monday' as test_name,
    slot_date,
    start_time,
    end_time,
    max_appointments,
    current_appointments,
    is_available,
    is_from_template
FROM get_available_slots_for_date(
    CURRENT_DATE + (1 - EXTRACT(DOW FROM CURRENT_DATE) + 7)::INTEGER % 7
)
ORDER BY start_time;

-- Test 5: Create a test appointment and verify slot capacity
INSERT INTO appointments (
    patient_name,
    patient_email,
    patient_phone,
    preferred_date,
    preferred_time,
    appointment_type,
    status,
    notes
) VALUES (
    'Test Patient',
    '<EMAIL>',
    '555-0123',
    CURRENT_DATE + 1,
    '09:30:00',
    'consultation',
    'confirmed',
    'Test appointment for availability system'
) ON CONFLICT DO NOTHING;

-- Test 6: Verify appointment affects availability
SELECT 
    'Test 6: Appointment Impact on Availability' as test_name,
    COUNT(*) as test_appointments_created
FROM appointments 
WHERE patient_name = 'Test Patient' 
AND preferred_date = CURRENT_DATE + 1;

-- Test 7: Check current_availability view
SELECT 
    'Test 7: Current Availability View' as test_name,
    display_date,
    start_time,
    end_time,
    max_appointments,
    current_appointments,
    is_recurring
FROM current_availability
LIMIT 10;

-- Test 8: Performance test - check query execution time
EXPLAIN ANALYZE 
SELECT * FROM get_available_slots_for_date(CURRENT_DATE + 7);

-- Test 9: Verify RLS policies work
SET ROLE authenticated;
SELECT 
    'Test 9: RLS Policy Test' as test_name,
    COUNT(*) as visible_slots
FROM availability_slots;
RESET ROLE;

-- Test 10: Clean up test data
DELETE FROM appointments WHERE patient_name = 'Test Patient';

-- Summary Report
SELECT 
    'SYSTEM TEST SUMMARY' as report_title,
    'Enhanced Availability Slots System' as system_name,
    CURRENT_TIMESTAMP as test_run_time,
    (SELECT COUNT(*) FROM availability_slots WHERE is_recurring = true) as recurring_slots,
    (SELECT COUNT(DISTINCT day_of_week) FROM availability_slots WHERE is_recurring = true) as days_covered,
    'Ready for production use' as status;

-- Recommendations for next steps
SELECT 
    'NEXT STEPS' as section,
    'Run the enhanced-availability-slots-schema.sql in Supabase' as step_1,
    'Test admin interface for slot management' as step_2,
    'Test patient booking with real-time availability' as step_3,
    'Monitor performance with real appointment data' as step_4;
