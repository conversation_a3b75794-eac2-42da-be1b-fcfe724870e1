-- =====================================================
-- SUPABASE AVAILABILITY SLOTS SYSTEM - CRITICAL FIXES
-- =====================================================
-- This script fixes the three critical issues:
-- 1. SQL syntax compatibility with Supabase
-- 2. Creates the missing get_available_slots_for_date function
-- 3. Ensures proper RPC access for the function

-- Step 1: Add missing columns with Supabase-compatible syntax
DO $$ 
BEGIN
    -- Add day_of_week column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'availability_slots' AND column_name = 'day_of_week') THEN
        ALTER TABLE availability_slots ADD COLUMN day_of_week INTEGER;
        RAISE NOTICE 'Added day_of_week column';
    END IF;
    
    -- Add is_recurring column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'availability_slots' AND column_name = 'is_recurring') THEN
        ALTER TABLE availability_slots ADD COLUMN is_recurring BOOLEAN DEFAULT FALSE;
        RAISE NOTICE 'Added is_recurring column';
    END IF;
    
    -- Add effective_from column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'availability_slots' AND column_name = 'effective_from') THEN
        ALTER TABLE availability_slots ADD COLUMN effective_from DATE DEFAULT CURRENT_DATE;
        RAISE NOTICE 'Added effective_from column';
    END IF;
    
    -- Add effective_until column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'availability_slots' AND column_name = 'effective_until') THEN
        ALTER TABLE availability_slots ADD COLUMN effective_until DATE;
        RAISE NOTICE 'Added effective_until column';
    END IF;
END $$;

-- Step 2: Add constraints with proper Supabase syntax
DO $$ 
BEGIN
    -- Add day_of_week constraint if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'chk_day_of_week' 
        AND table_name = 'availability_slots'
    ) THEN
        ALTER TABLE availability_slots 
        ADD CONSTRAINT chk_day_of_week CHECK (day_of_week IS NULL OR (day_of_week >= 0 AND day_of_week <= 6));
        RAISE NOTICE 'Added day_of_week constraint';
    END IF;
END $$;

-- Step 3: Create indexes for better performance
DO $$ 
BEGIN
    -- Create day_of_week index if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_availability_slots_day_of_week') THEN
        CREATE INDEX idx_availability_slots_day_of_week ON availability_slots(day_of_week);
        RAISE NOTICE 'Created day_of_week index';
    END IF;
    
    -- Create is_recurring index if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_availability_slots_recurring') THEN
        CREATE INDEX idx_availability_slots_recurring ON availability_slots(is_recurring);
        RAISE NOTICE 'Created is_recurring index';
    END IF;
    
    -- Create effective_dates index if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_availability_slots_effective_dates') THEN
        CREATE INDEX idx_availability_slots_effective_dates ON availability_slots(effective_from, effective_until);
        RAISE NOTICE 'Created effective_dates index';
    END IF;
END $$;

-- Step 4: Create the get_available_slots_for_date function (CRITICAL FIX)
CREATE OR REPLACE FUNCTION get_available_slots_for_date(target_date DATE)
RETURNS TABLE (
    slot_id UUID,
    slot_date DATE,
    start_time TIME,
    end_time TIME,
    max_appointments INTEGER,
    current_appointments INTEGER,
    is_available BOOLEAN,
    notes TEXT,
    is_from_template BOOLEAN
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    -- First, get specific date slots (non-recurring)
    SELECT 
        a.id as slot_id,
        a.date as slot_date,
        a.start_time,
        a.end_time,
        a.max_appointments,
        COALESCE(a.current_appointments, 0) as current_appointments,
        a.is_available,
        a.notes,
        FALSE as is_from_template
    FROM availability_slots a
    WHERE a.date = target_date 
    AND COALESCE(a.is_recurring, FALSE) = FALSE
    AND COALESCE(a.is_available, TRUE) = TRUE
    
    UNION ALL
    
    -- Then, get recurring slots for this day of week (if no specific slot exists)
    SELECT 
        a.id as slot_id,
        target_date as slot_date,
        a.start_time,
        a.end_time,
        a.max_appointments,
        COALESCE((
            SELECT COUNT(*)::INTEGER 
            FROM appointments apt 
            WHERE apt.preferred_date = target_date 
            AND apt.preferred_time >= a.start_time 
            AND apt.preferred_time < a.end_time
            AND apt.status IN ('pending', 'confirmed', 'in_progress')
        ), 0) as current_appointments,
        a.is_available,
        a.notes,
        TRUE as is_from_template
    FROM availability_slots a
    WHERE COALESCE(a.is_recurring, FALSE) = TRUE
    AND a.day_of_week = EXTRACT(DOW FROM target_date)
    AND (a.effective_from IS NULL OR target_date >= a.effective_from)
    AND (a.effective_until IS NULL OR target_date <= a.effective_until)
    AND COALESCE(a.is_available, TRUE) = TRUE
    AND NOT EXISTS (
        -- Don't include if there's already a specific slot for this time
        SELECT 1 FROM availability_slots specific
        WHERE specific.date = target_date
        AND specific.start_time = a.start_time
        AND specific.end_time = a.end_time
        AND COALESCE(specific.is_recurring, FALSE) = FALSE
    )
    
    ORDER BY start_time;
END;
$$;

-- Step 5: Grant necessary permissions for RPC access
GRANT EXECUTE ON FUNCTION get_available_slots_for_date(DATE) TO anon;
GRANT EXECUTE ON FUNCTION get_available_slots_for_date(DATE) TO authenticated;

-- Step 6: Insert sample recurring slots for testing
INSERT INTO availability_slots (
    day_of_week, 
    start_time, 
    end_time, 
    is_available, 
    max_appointments, 
    current_appointments, 
    notes, 
    is_recurring,
    effective_from
) VALUES
-- Monday to Friday morning slots
(1, '09:00:00', '12:00:00', true, 6, 0, 'Monday morning appointments', true, CURRENT_DATE),
(2, '09:00:00', '12:00:00', true, 6, 0, 'Tuesday morning appointments', true, CURRENT_DATE),
(3, '09:00:00', '12:00:00', true, 6, 0, 'Wednesday morning appointments', true, CURRENT_DATE),
(4, '09:00:00', '12:00:00', true, 6, 0, 'Thursday morning appointments', true, CURRENT_DATE),
(5, '09:00:00', '12:00:00', true, 6, 0, 'Friday morning appointments', true, CURRENT_DATE),

-- Monday to Friday afternoon slots
(1, '14:00:00', '18:00:00', true, 8, 0, 'Monday afternoon appointments', true, CURRENT_DATE),
(2, '14:00:00', '18:00:00', true, 8, 0, 'Tuesday afternoon appointments', true, CURRENT_DATE),
(3, '14:00:00', '18:00:00', true, 8, 0, 'Wednesday afternoon appointments', true, CURRENT_DATE),
(4, '14:00:00', '18:00:00', true, 8, 0, 'Thursday afternoon appointments', true, CURRENT_DATE),
(5, '14:00:00', '18:00:00', true, 8, 0, 'Friday afternoon appointments', true, CURRENT_DATE),

-- Saturday morning slot
(6, '09:00:00', '13:00:00', true, 8, 0, 'Saturday morning appointments', true, CURRENT_DATE)

ON CONFLICT DO NOTHING;

-- Step 7: Update RLS policies to handle the new columns
DROP POLICY IF EXISTS "Anyone can view availability slots" ON availability_slots;
CREATE POLICY "Anyone can view availability slots" ON availability_slots
  FOR SELECT USING (COALESCE(is_available, TRUE) = TRUE);

-- Step 8: Test the function to ensure it works
SELECT 'Testing get_available_slots_for_date function...' as status;

-- Test for today
SELECT 
    slot_date,
    start_time,
    end_time,
    max_appointments,
    current_appointments,
    is_from_template
FROM get_available_slots_for_date(CURRENT_DATE)
ORDER BY start_time
LIMIT 5;

-- Success message
SELECT 
    'SUCCESS: All critical issues fixed!' as status,
    'Function created and accessible via RPC' as function_status,
    'Columns added with Supabase-compatible syntax' as schema_status,
    'Sample data inserted for testing' as data_status;
