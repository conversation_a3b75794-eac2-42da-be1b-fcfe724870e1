-- =====================================================
-- CONSOLIDATED AVAILABILITY SLOTS FIX - SINGLE MIGRATION
-- =====================================================
-- This script fixes both critical database errors:
-- 1. Missing 'is_recurring' column causing admin API error
-- 2. Missing 'get_available_slots_for_date' function causing patient API error
-- 3. Resolves NOT NULL constraint violation on 'date' column for recurring slots
--
-- CRITICAL FIX: Makes 'date' column nullable to support recurring templates
-- =====================================================

-- Step 1: CRITICAL - Remove NOT NULL constraint from date column to support recurring slots
DO $$
BEGIN
    -- Check if the NOT NULL constraint exists and remove it
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'availability_slots'
        AND column_name = 'date'
        AND is_nullable = 'NO'
    ) THEN
        ALTER TABLE availability_slots ALTER COLUMN date DROP NOT NULL;
        RAISE NOTICE 'Removed NOT NULL constraint from date column to support recurring slots';
    END IF;
END $$;

-- Step 2: Add missing columns with Supabase-compatible syntax
DO $$
BEGIN
    -- Add day_of_week column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'availability_slots' AND column_name = 'day_of_week') THEN
        ALTER TABLE availability_slots ADD COLUMN day_of_week INTEGER;
        RAISE NOTICE 'Added day_of_week column';
    END IF;

    -- Add is_recurring column if it doesn't exist (CRITICAL for admin API)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'availability_slots' AND column_name = 'is_recurring') THEN
        ALTER TABLE availability_slots ADD COLUMN is_recurring BOOLEAN DEFAULT FALSE;
        RAISE NOTICE 'Added is_recurring column - FIXES admin API error';
    END IF;

    -- Add effective_from column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'availability_slots' AND column_name = 'effective_from') THEN
        ALTER TABLE availability_slots ADD COLUMN effective_from DATE DEFAULT CURRENT_DATE;
        RAISE NOTICE 'Added effective_from column';
    END IF;

    -- Add effective_until column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'availability_slots' AND column_name = 'effective_until') THEN
        ALTER TABLE availability_slots ADD COLUMN effective_until DATE;
        RAISE NOTICE 'Added effective_until column';
    END IF;
END $$;

-- Step 3: Add constraints with proper Supabase syntax
DO $$
BEGIN
    -- Add day_of_week constraint if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'chk_day_of_week'
        AND table_name = 'availability_slots'
    ) THEN
        ALTER TABLE availability_slots
        ADD CONSTRAINT chk_day_of_week CHECK (day_of_week IS NULL OR (day_of_week >= 0 AND day_of_week <= 6));
        RAISE NOTICE 'Added day_of_week constraint';
    END IF;

    -- Add constraint to ensure either date OR day_of_week is set (but not both)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'chk_date_or_recurring'
        AND table_name = 'availability_slots'
    ) THEN
        ALTER TABLE availability_slots
        ADD CONSTRAINT chk_date_or_recurring CHECK (
            (date IS NOT NULL AND is_recurring = FALSE AND day_of_week IS NULL) OR
            (date IS NULL AND is_recurring = TRUE AND day_of_week IS NOT NULL)
        );
        RAISE NOTICE 'Added date_or_recurring constraint to ensure data integrity';
    END IF;
END $$;

-- Step 4: Create indexes for better performance
DO $$
BEGIN
    -- Create day_of_week index if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_availability_slots_day_of_week') THEN
        CREATE INDEX idx_availability_slots_day_of_week ON availability_slots(day_of_week);
        RAISE NOTICE 'Created day_of_week index';
    END IF;

    -- Create is_recurring index if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_availability_slots_recurring') THEN
        CREATE INDEX idx_availability_slots_recurring ON availability_slots(is_recurring);
        RAISE NOTICE 'Created is_recurring index';
    END IF;

    -- Create effective_dates index if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_availability_slots_effective_dates') THEN
        CREATE INDEX idx_availability_slots_effective_dates ON availability_slots(effective_from, effective_until);
        RAISE NOTICE 'Created effective_dates index';
    END IF;
END $$;

-- Step 5: Create the get_available_slots_for_date function (CRITICAL FIX for patient API)
CREATE OR REPLACE FUNCTION get_available_slots_for_date(target_date DATE)
RETURNS TABLE (
    slot_id UUID,
    slot_date DATE,
    start_time TIME,
    end_time TIME,
    max_appointments INTEGER,
    current_appointments INTEGER,
    is_available BOOLEAN,
    notes TEXT,
    is_from_template BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    -- First, get specific date slots (non-recurring)
    SELECT
        a.id as slot_id,
        a.date as slot_date,
        a.start_time,
        a.end_time,
        a.max_appointments,
        COALESCE(a.current_appointments, 0) as current_appointments,
        a.is_available,
        a.notes,
        FALSE as is_from_template
    FROM availability_slots a
    WHERE a.date = target_date
    AND COALESCE(a.is_recurring, FALSE) = FALSE
    AND COALESCE(a.is_available, TRUE) = TRUE

    UNION ALL

    -- Then, get recurring slots for this day of week (if no specific slot exists)
    SELECT
        a.id as slot_id,
        target_date as slot_date,
        a.start_time,
        a.end_time,
        a.max_appointments,
        COALESCE((
            SELECT COUNT(*)::INTEGER
            FROM appointments apt
            WHERE apt.preferred_date = target_date
            AND apt.preferred_time >= a.start_time
            AND apt.preferred_time < a.end_time
            AND apt.status IN ('pending', 'confirmed', 'in_progress')
        ), 0) as current_appointments,
        a.is_available,
        a.notes,
        TRUE as is_from_template
    FROM availability_slots a
    WHERE COALESCE(a.is_recurring, FALSE) = TRUE
    AND a.day_of_week = EXTRACT(DOW FROM target_date)
    AND (a.effective_from IS NULL OR target_date >= a.effective_from)
    AND (a.effective_until IS NULL OR target_date <= a.effective_until)
    AND COALESCE(a.is_available, TRUE) = TRUE
    AND NOT EXISTS (
        -- Don't include if there's already a specific slot for this time
        SELECT 1 FROM availability_slots specific
        WHERE specific.date = target_date
        AND specific.start_time = a.start_time
        AND specific.end_time = a.end_time
        AND COALESCE(specific.is_recurring, FALSE) = FALSE
    )

    ORDER BY start_time;
END;
$$;

-- Step 6: Grant necessary permissions for RPC access (CRITICAL for patient API)
GRANT EXECUTE ON FUNCTION get_available_slots_for_date(DATE) TO anon;
GRANT EXECUTE ON FUNCTION get_available_slots_for_date(DATE) TO authenticated;

-- Step 7: Update RLS policies to handle the new columns
DROP POLICY IF EXISTS "Anyone can view availability slots" ON availability_slots;
CREATE POLICY "Anyone can view availability slots" ON availability_slots
  FOR SELECT USING (COALESCE(is_available, TRUE) = TRUE);

-- =====================================================
-- NO SAMPLE DATA INSERTION
-- =====================================================
-- As requested, we do NOT insert any sample/test data.
-- The system should work with real data created through the admin interface.
-- The admin can now create both:
-- 1. Specific date slots (date NOT NULL, is_recurring = FALSE, day_of_week = NULL)
-- 2. Recurring template slots (date = NULL, is_recurring = TRUE, day_of_week = 0-6)

-- Step 8: Test the function to ensure it works (without requiring sample data)
SELECT 'Testing get_available_slots_for_date function...' as status;

-- Test the function exists and is callable
SELECT
    'Function get_available_slots_for_date exists and is callable' as test_result,
    COUNT(*) as slot_count
FROM get_available_slots_for_date(CURRENT_DATE);

-- =====================================================
-- MIGRATION COMPLETE - SUMMARY
-- =====================================================
SELECT
    'SUCCESS: All critical database errors fixed!' as status,
    'Admin API: is_recurring column added' as admin_fix,
    'Patient API: get_available_slots_for_date function created' as patient_fix,
    'Schema: date column made nullable for recurring slots' as constraint_fix,
    'Ready for real data through admin interface' as data_status;

-- =====================================================
-- NEXT STEPS FOR ADMIN
-- =====================================================
-- The admin interface can now create:
--
-- 1. SPECIFIC DATE SLOTS:
--    INSERT INTO availability_slots (date, start_time, end_time, is_available, max_appointments, is_recurring)
--    VALUES ('2025-09-30', '09:00:00', '12:00:00', true, 6, false);
--
-- 2. RECURRING TEMPLATE SLOTS:
--    INSERT INTO availability_slots (day_of_week, start_time, end_time, is_available, max_appointments, is_recurring, effective_from)
--    VALUES (1, '09:00:00', '12:00:00', true, 6, true, CURRENT_DATE);  -- Monday template
--
-- Both APIs will now work correctly without database errors.
