import { NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-server'

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { data: slot, error } = await supabaseAdmin
      .from('availability_slots')
      .select('*')
      .eq('id', params.id)
      .single()

    if (error) throw error

    return NextResponse.json(slot)
  } catch (error) {
    console.error('Error fetching availability slot:', error)
    return NextResponse.json(
      { error: 'Failed to fetch availability slot' },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    
    // Build update object with only provided fields
    const updateData: any = {}
    if (body.date !== undefined) updateData.date = body.date
    if (body.day_of_week !== undefined) updateData.day_of_week = body.day_of_week
    if (body.start_time !== undefined) updateData.start_time = body.start_time
    if (body.end_time !== undefined) updateData.end_time = body.end_time
    if (body.is_available !== undefined) updateData.is_available = body.is_available
    if (body.max_appointments !== undefined) updateData.max_appointments = body.max_appointments
    if (body.is_recurring !== undefined) updateData.is_recurring = body.is_recurring
    if (body.effective_from !== undefined) updateData.effective_from = body.effective_from
    if (body.effective_until !== undefined) updateData.effective_until = body.effective_until
    if (body.notes !== undefined) updateData.notes = body.notes

    const { data: slot, error } = await supabaseAdmin
      .from('availability_slots')
      .update(updateData)
      .eq('id', params.id)
      .select()
      .single()

    if (error) throw error

    return NextResponse.json(slot)
  } catch (error) {
    console.error('Error updating availability slot:', error)
    return NextResponse.json(
      { error: 'Failed to update availability slot' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { error } = await supabaseAdmin
      .from('availability_slots')
      .delete()
      .eq('id', params.id)

    if (error) throw error

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting availability slot:', error)
    return NextResponse.json(
      { error: 'Failed to delete availability slot' },
      { status: 500 }
    )
  }
}
