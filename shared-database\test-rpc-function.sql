-- =====================================================
-- TEST RPC FUNCTION FOR AVAILABILITY SLOTS
-- =====================================================
-- This script tests the get_available_slots_for_date function
-- to ensure it works correctly with Supabase RPC calls

-- Test 1: Check if function exists
SELECT 
    'Test 1: Function Existence' as test_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.routines 
            WHERE routine_name = 'get_available_slots_for_date'
            AND routine_type = 'FUNCTION'
        ) THEN 'PASS - Function exists'
        ELSE 'FAIL - Function not found'
    END as result;

-- Test 2: Check function permissions
SELECT 
    'Test 2: Function Permissions' as test_name,
    routine_name,
    routine_type,
    security_type
FROM information_schema.routines 
WHERE routine_name = 'get_available_slots_for_date';

-- Test 3: Test function with current date
SELECT 
    'Test 3: Function Call for Today' as test_name,
    COUNT(*) as slots_returned
FROM get_available_slots_for_date(CURRENT_DATE);

-- Test 4: Test function with specific date (next Monday)
SELECT 
    'Test 4: Function Call for Next Monday' as test_name,
    COUNT(*) as slots_returned
FROM get_available_slots_for_date(
    CURRENT_DATE + (1 - EXTRACT(DOW FROM CURRENT_DATE) + 7)::INTEGER % 7
);

-- Test 5: Detailed results for today
SELECT 
    'Test 5: Detailed Results for Today' as test_name,
    slot_date,
    start_time,
    end_time,
    max_appointments,
    current_appointments,
    is_available,
    is_from_template
FROM get_available_slots_for_date(CURRENT_DATE)
ORDER BY start_time;

-- Test 6: Check recurring slots exist
SELECT 
    'Test 6: Recurring Slots Check' as test_name,
    COUNT(*) as recurring_slots_count,
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS - Recurring slots exist'
        ELSE 'FAIL - No recurring slots found'
    END as result
FROM availability_slots 
WHERE is_recurring = true;

-- Test 7: Check day-of-week distribution
SELECT 
    'Test 7: Day Distribution' as test_name,
    day_of_week,
    CASE day_of_week
        WHEN 0 THEN 'Sunday'
        WHEN 1 THEN 'Monday'
        WHEN 2 THEN 'Tuesday'
        WHEN 3 THEN 'Wednesday'
        WHEN 4 THEN 'Thursday'
        WHEN 5 THEN 'Friday'
        WHEN 6 THEN 'Saturday'
        ELSE 'Unknown'
    END as day_name,
    COUNT(*) as slot_count
FROM availability_slots 
WHERE is_recurring = true
GROUP BY day_of_week
ORDER BY day_of_week;

-- Test 8: Simulate RPC call format (this is how Supabase will call it)
SELECT 
    'Test 8: RPC Call Simulation' as test_name,
    'This simulates how the API will call the function' as description;

-- The actual RPC call from JavaScript would be:
-- supabase.rpc('get_available_slots_for_date', { target_date: '2025-09-27' })

-- Test 9: Performance test
EXPLAIN ANALYZE 
SELECT * FROM get_available_slots_for_date(CURRENT_DATE + 1);

-- Test 10: Final status check
SELECT 
    'FINAL STATUS CHECK' as section,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'get_available_slots_for_date')
        AND EXISTS (SELECT 1 FROM availability_slots WHERE is_recurring = true)
        THEN 'SUCCESS: System ready for production'
        ELSE 'ERROR: Issues detected'
    END as overall_status,
    (SELECT COUNT(*) FROM availability_slots WHERE is_recurring = true) as recurring_slots,
    'Run this in Supabase SQL Editor to verify everything works' as instructions;
